import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import User from 'App/Models/User'
import { baseUrlAdmin } from 'App/utils'
import AppVersionControl from 'App/Models/AppVersionControl'

test.group('App Version Control Controller Admin', async (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 createVersion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .post(`${baseUrlAdmin}/app-version-controls`)
      .json({
        version: '10.0.0',
        description: 'version 10.0.0 migrated adonisjs',
        publish_now: true,
      })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'version',
      'description',
      'added_by',
      'published_by',
      'published_at',
      'id',
      'created_at',
      'updated_at',
    ])
  })

  test('1.2 createVersion - version not match regex', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .post(`${baseUrlAdmin}/app-version-controls`)
      .json({
        version: '10.0.a',
        description: 'version 10.0.a migrated adonisjs',
        publish_now: true,
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      errors: [
        {
          rule: 'regex',
          field: 'version',
          message: 'regex validation failed',
        },
      ],
    })
  })

  test('2.1 retrieveVersion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.query().firstOrFail()
    const response = await client
      .get(`${baseUrlAdmin}/app-version-controls/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'version',
      'description',
      'added_by',
      'published_by',
      'published_at',
      'id',
      'created_at',
      'updated_at',
    ])
  })

  test('2.2 retrieveVersion - app version not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/app-version-controls/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'App Version not found',
    })
  })

  test('3 retrieveVersions - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/app-version-controls`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'version',
        'description',
        'added_by',
        'published_by',
        'published_at',
        'id',
        'created_at',
        'updated_at',
      ])
    }
  })

  test('4.1 updateVersion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '2.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/${appVersion.id}`)
      .json({
        version: '2.0.23',
        description: 'updated version 2.0.0 to 2.0.23 description',
      })
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'version',
      'description',
      'added_by',
      'published_by',
      'published_at',
      'id',
      'created_at',
      'updated_at',
    ])
  })

  test('4.2 updateVersion - existing version (not unique)', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '2.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/${appVersion.id}`)
      .json({
        version: '1.0.0',
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      errors: [
        {
          rule: 'unique',
          field: 'version',
          message: 'unique validation failure',
        },
      ],
    })
  })

  test('4.3 updateVersion - version not match regex', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '2.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/${appVersion.id}`)
      .json({
        version: '1.0.ab',
      })
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      errors: [
        {
          rule: 'regex',
          field: 'version',
          message: 'regex validation failed',
        },
      ],
    })
  })

  test('5.1 publishVersion - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '6.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/publish/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: true, message: 'Successfully published the version' })
  })

  test('5.2 publishVersion - already published', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '1.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/publish/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'The version has already been published',
    })
  })

  test('5.3 publishVersion - version not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/publish/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ message: 'E_ROW_NOT_FOUND: Row not found' })
  })

  test('6.1 unpublishVersion - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '3.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/unpublish/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: true, message: 'Successfully unpublished the version' })
  })

  test('6.2 unpublishVersion - already unpublished', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '6.0.0')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/unpublish/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'The version has not been published' })
  })

  test('6.3 unpublishVersion - version not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .put(`${baseUrlAdmin}/app-version-controls/unpublish/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ message: 'E_ROW_NOT_FOUND: Row not found' })
  })

  test('7.1 deleteVersion - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const appVersion = await AppVersionControl.findByOrFail('version', '6.0.0')
    const response = await client
      .delete(`${baseUrlAdmin}/app-version-controls/${appVersion.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: true, message: 'Successfully deleted the version' })
  })

  test('7.2 deleteVersion - version not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .delete(`${baseUrlAdmin}/app-version-controls/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ message: 'E_ROW_NOT_FOUND: Row not found' })
  })
})
