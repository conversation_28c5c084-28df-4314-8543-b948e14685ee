import Database from '@ioc:Adonis/Lucid/Database'
import { test } from '@japa/runner'
import ListingFile from 'App/Models/ListingFile'
import User from 'App/Models/User'

test.group('Listing File Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1.1 findListingFileAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const listingFile = await ListingFile.query().where('is_approved', false).limit(1).first()
    const response = await client
      .get(`/api/v1/admin/listing-files/${listingFile?.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'id',
      'listing_id',
      'uploaded_by',
      'image_url',
      'is_main',
      'is_approved',
      'is_hidden',
      'reviewed_by',
      'not_approved_reason',
      'created_at',
      'updated_at',
    ])
  })

  test('1.2 findListingFileAdmin - listing file not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`/api/v1/admin/listing-files/${'not_exist_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Listing not found',
    })
  })

  test('2 findListingFilesAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`/api/v1/admin/listing-files`).guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'id',
        'listing_id',
        'uploaded_by',
        'image_url',
        'is_main',
        'is_approved',
        'is_hidden',
        'reviewed_by',
        'not_approved_reason',
        'created_at',
        'updated_at',
      ])
    }
  })
})
