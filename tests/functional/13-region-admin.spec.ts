import { test } from '@japa/runner'
import Drive from '@ioc:Adonis/Core/Drive'
import { file } from '@ioc:Adonis/Core/Helpers'
import { baseUrlAdmin, remotePathRegion } from 'App/utils'
import User from 'App/Models/User'
import Region from 'App/Models/Region'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Region Controller Admin', (group) => {
  group.each.setup(async () => {
    await Database.beginGlobalTransaction()
    return () => Database.rollbackGlobalTransaction()
  })

  test('1 createRegion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeDrive = Drive.fake()
    const fakeImg1 = await file.generatePng('1mb')

    const response = await client
      .post(`${baseUrlAdmin}/regions`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: 'Nowhere',
        description: 'nowhere test case',
        image_url:
          'https://c8.alamy.com/comp/GP9D8E/blue-signpost-direction-signs-here-there-anywhere-nowhere-somewhere-GP9D8E.jpg',
      })
      .file('image_file', fakeImg1.contents, { filename: fakeImg1.name })

    assert.isTrue(await fakeDrive.exists(remotePathRegion + '/' + fakeImg1.name))

    Drive.restore()

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('1.1 createRegion - region name already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const regionName = 'Central'

    const response = await client
      .post(`${baseUrlAdmin}/regions`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: regionName,
        description: 'Central test case',
        image_url:
          'https://c8.alamy.com/comp/GP9D8E/blue-signpost-direction-signs-here-there-anywhere-nowhere-somewhere-GP9D8E.jpg',
      })

    response.assertBodyContains({
      success: false,
      message: `The region: ${regionName} already exist`,
    })
  })

  test('2 findRegionsAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client.get(`${baseUrlAdmin}/regions`).guard('web').loginAs(user!)

    const data = response.body().data

    if (data.length > 0) {
      assert.properties(data[0], [
        'name',
        'description',
        'slug',
        'id',
        'created_at',
        'updated_at',
        'image_url',
      ])
    }
  })

  test('3.1 findRegionAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('name', 'Central')
    const response = await client
      .get(`${baseUrlAdmin}/regions/${region.id}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('3.2 findRegionAdmin - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/regions/${'not_existing_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })

  test('4.1 findRegionWithSlugAdmin - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('name', 'Central')
    const response = await client
      .get(`${baseUrlAdmin}/regions/search-slug/${region.slug}`)
      .guard('web')
      .loginAs(user!)

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('4.2 findRegionWithSlugAdmin - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const response = await client
      .get(`${baseUrlAdmin}/regions/search-slug/${'not_existing_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })

  test('5.1 updateRegion - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeDrive = Drive.fake()
    const fakeImg1 = await file.generatePng('1mb')
    const region = await Region.findByOrFail('slug', 'to-update-region')

    const response = await client
      .put(`${baseUrlAdmin}/regions/${region.id}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: 'to update region - updated',
        description: 'Middle test case update',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })
      .file('image_file', fakeImg1.contents, { filename: fakeImg1.name })

    assert.isTrue(await fakeDrive.exists(remotePathRegion + '/' + fakeImg1.name))
    Drive.restore()

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])
  })

  test('5.2 updateRegion - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .put(`${baseUrlAdmin}/regions/${'not_existing_region'}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: 'Middle Update',
        description: 'Middle test case update',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })

  test('5.3 updateRegion - region already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'to-update-region')
    const updateName = 'Central'

    const response = await client
      .put(`${baseUrlAdmin}/regions/${region.id}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: updateName,
        description: 'central update',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })

    response.assertBodyContains({
      success: false,
      message: `The region: ${updateName} already exist`,
    })
  })

  test('6.1 updateRegionWithSlug - success', async ({ assert, client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const fakeDrive = Drive.fake()
    const fakeImg1 = await file.generatePng('1mb')
    const region = await Region.findByOrFail('slug', 'to-update-region-slug')

    const response = await client
      .put(`${baseUrlAdmin}/regions/slug/${region.slug}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: 'Middle Update With Slug',
        description: 'Middle test case update with slug',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })
      .file('image_file', fakeImg1.contents, { filename: fakeImg1.name })

    const data = response.body().data

    assert.properties(data, [
      'name',
      'description',
      'slug',
      'id',
      'created_at',
      'updated_at',
      'image_url',
    ])

    assert.isTrue(await fakeDrive.exists(remotePathRegion + '/' + fakeImg1.name))
    Drive.restore()
  })

  test('6.2 updateRegionWithSlug - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .put(`${baseUrlAdmin}/regions/slug/${'not_existing_slug'}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: 'Middle Update',
        description: 'Middle test case update',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })

    response.assertBodyContains({ success: false, message: 'Region not found' })
  })

  test('6.3 updateRegionWithSlug - region already exist', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'to-update-region-slug')
    const updateName = 'Central'

    const response = await client
      .put(`${baseUrlAdmin}/regions/slug/${region.slug}`)
      .guard('web')
      .loginAs(user!)
      .fields({
        name: updateName,
        description: 'central update',
        image_url:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcScxuSlcxRyRCKmEk5rferzO5EueCmCAoqNbw&s',
        is_hidden: false,
      })

    response.assertBodyContains({
      success: false,
      message: `The region: ${updateName} already exist`,
    })
  })

  test('7.1 deleteRegion - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'to-delete-region')

    const response = await client
      .delete(`${baseUrlAdmin}/regions/${region.id}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted a region',
    })
  })

  test('7.2 deleteRegion - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .delete(`${baseUrlAdmin}/regions/${'not_existing_region_id'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Region not found',
    })
  })

  test('8.1 deleteRegionWithSlug - success', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')
    const region = await Region.findByOrFail('slug', 'to-delete-region-slug')

    const response = await client
      .delete(`${baseUrlAdmin}/regions/slug/${region.slug}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: true,
      message: 'Successfully deleted a region',
    })
  })

  test('8.2 deleteRegionWithSlug - region not found', async ({ client }) => {
    const user = await User.findByOrFail('email_address', '<EMAIL>')

    const response = await client
      .delete(`${baseUrlAdmin}/regions/slug/${'not_existing_slug'}`)
      .guard('web')
      .loginAs(user!)

    response.assertBodyContains({
      success: false,
      message: 'Region not found',
    })
  })
})
