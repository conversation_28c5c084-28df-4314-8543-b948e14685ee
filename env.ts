/*
|--------------------------------------------------------------------------
| Validating Environment Variables
|--------------------------------------------------------------------------
|
| In this file we define the rules for validating environment variables.
| By performing validation we ensure that your application is running in
| a stable environment with correct configuration values.
|
| This file is read automatically by the framework during the boot lifecycle
| and hence do not rename or move this file to a different location.
|
*/

import Env from '@ioc:Adonis/Core/Env'

export default Env.rules({
  HOST: Env.schema.string({ format: 'host' }),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  APP_NAME: Env.schema.string(),
  PUBLIC_DRIVE_DISK: Env.schema.enum(['local', 's3_public', 's3_private'] as const),
  PRIVATE_DRIVE_DISK: Env.schema.enum(['local', 's3_public', 's3_private'] as const),
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),

  MYSQL_HOST: Env.schema.string({ format: 'host' }),
  MYSQL_PORT: Env.schema.number(),
  MYSQL_USER: Env.schema.string(),
  MYSQL_PASSWORD: Env.schema.string.optional(),
  MYSQL_DB_NAME: Env.schema.string(),

  S3_ENDPOINT: Env.schema.string(),
  S3_PRIVATE_ENDPOINT: Env.schema.string(),
  S3_PUBLIC_ENDPOINT: Env.schema.string(),
  USER_VERIFY_IMAGE_URL: Env.schema.string(),
  USER_PROFILE_IMAGE_URL: Env.schema.string(),
  LISTING_IMAGE_URL: Env.schema.string(),
  ACTIVITY_IMAGE_URL: Env.schema.string(),
  AMENITY_IMAGE_URL: Env.schema.string(),
  REGION_IMAGE_URL: Env.schema.string(),
  LISTING_FLAG_IMAGE_URL: Env.schema.string(),
  NOTIFICATION_MESSAGE_IMAGE_URL: Env.schema.string(),

  IGLOOHOME_API_KEY: Env.schema.string(),
  IGLOOHOME_DISTANCE: Env.schema.number(),
  IGLOOHOME_ENTRY_TIMEOUT: Env.schema.number(),

  SESSION_DRIVER: Env.schema.string(),

  CIPHER_SECRET: Env.schema.string(),
  CIPHER_IV: Env.schema.string(),
  SINGPASS_CLIENT_ID: Env.schema.string(),
  SINGPASS_CLIENT_SECRET: Env.schema.string(),
  SINGPASS_SCOPE: Env.schema.string(),
  SINGPASS_GRANT_TYPE: Env.schema.string(),
  SINGPASS_MYINFO_URL: Env.schema.string(),
  SINGPASS_PURPOSE: Env.schema.string(),
  SINGPASS_REDIRECT_URI: Env.schema.string(),
  SINGPASS_PKI_COMPANY_PRIVATE_KEY: Env.schema.string(),
  SINGPASS_PKI_MYINFO_PUBLIC_CERT: Env.schema.string(),

  BASE_URL: Env.schema.string(),
  BASE_URL_ADMIN: Env.schema.string(),

  GOOGLE_CLIENT_ID: Env.schema.string(),
  GOOGLE_CLIENT_SECRET: Env.schema.string(),

  FACEBOOK_CLIENT_ID: Env.schema.string(),
  FACEBOOK_CLIENT_SECRET: Env.schema.string(),
  FACEBOOK_CALLBACK_URL: Env.schema.string(),

  APPLE_APP_ID: Env.schema.string(),
  APPLE_TEAM_ID: Env.schema.string(),
  APPLE_CLIENT_ID: Env.schema.string(),
  APPLE_CLIENT_SECRET: Env.schema.string(),

  MEILISEARCH_ADMIN_KEY: Env.schema.string(),
  MEILISEARCH_SEARCH_KEY: Env.schema.string(),
  MEILISEARCH_BASE_URL: Env.schema.string(),

  SEEDER_RUN_TEST_CASE: Env.schema.boolean(), // to run dummy data in seeder instead of production data

  MAX_VERIFY_SELFIE_FAIL_TRIES: Env.schema.number(), // max number of verify selfie tries fail.

  ZEST_REVIEW_BASE_URL: Env.schema.string(),
  ZEST_REVIEW_API_KEY: Env.schema.string(),
  ZEST_REVIEW_APP_CLIENT_ID: Env.schema.string(),
  ZEST_REVIEW_USERNAME: Env.schema.string(),
  ZEST_REVIEW_PASSWORD: Env.schema.string(),
  ZEST_REVIEW_ACCESS_TOKEN: Env.schema.string(),

  ZEST_LOYALTY_BASE_URL: Env.schema.string(),
  ZEST_LOYALTY_API_KEY: Env.schema.string(),
  ZEST_LOYALTY_APP_CLIENT_ID: Env.schema.string(),
  ZEST_LOYALTY_ACCESS_TOKEN: Env.schema.string(),

  ONEWAYSMS_USERNAME: Env.schema.string(),
  ONEWAYSMS_PASSWORD: Env.schema.string(),
  ONEWAYSMS_SENDER_ID: Env.schema.string(),

  API_KEY: Env.schema.string(),
})
