import meilisearchAdmin from 'App/Network/meilisearchAdmin'
import meilisearchClient from 'App/Network/meilisearchClient'
import { ListingType } from 'Contracts/listing_type'
import { calculateDistance } from 'App/Models/Listing'

type SearchQueries = {
  indexUid: string
  q: string
  offset?: number
  limit?: number
  hitsPerPage?: number
  page?: number
  filter?: string
  attributesToRetrieve?: string[]
  sort?: string[]
  matchingStrategy?: string
  attributesToSearchOn?: string[]
}[]

const search = {
  multiSearch: async (queries: SearchQueries) =>
    await meilisearchClient.post(`/multi-search`, { queries }),
}

const admin = {
  // Search
  searchListingsIndexWithPosition: async (
    lat: number, // to filter
    lon: number, // to filter
    distance: number,
    type: string,
    amenities: string[] = [],
    sort: string,
    page: number,
    limit: number,
    isAdminSearch: boolean = false,
    deviceLat?: number,
    deviceLon?: number
  ) => {
    let sortAttribute: string[] = []

    if (sort.includes('name')) {
      sortAttribute = []
      sortAttribute[0] = 'listing_name:asc'
      sortAttribute[1] = `_geoPoint(${lat}, ${lon}):asc`
    }

    if (sort.includes('rating')) {
      sortAttribute = []
      sortAttribute[0] = 'average_experience_ratings:desc'
      sortAttribute[1] = `_geoPoint(${lat}, ${lon}):asc`
    }

    if (sort.includes('distance')) {
      sortAttribute = []
      if (deviceLat !== undefined && deviceLon !== undefined) {
        sortAttribute[0] = `_geoPoint(${deviceLat}, ${deviceLon}):asc`
      } else {
        sortAttribute[0] = `_geoPoint(${lat}, ${lon}):asc`
      }
    }

    const response = await meilisearchAdmin.post(`/indexes/listings/search`, {
      filter: `_geoRadius(${lat}, ${lon}, ${distance})${
        isAdminSearch ? '' : ' AND listing_is_verified = true AND listing_is_hidden = false'
      }${
        amenities.length > 0
          ? ` AND amenities = ${amenities[0]}${amenities
              .map((amenity, index) => (index > 0 ? `amenities = ${amenity}` : ''))
              .join(' AND ')}`
          : ''
      }${
        type == 'all'
          ? ''
          : ` AND listing_type = ${
              type == ListingType.care ? 'care' : type == ListingType.gomama ? 'gomama' : ''
            }`
      }`,
      attributesToRetrieve: ['id', '_geo', 'coordinate'],
      sort: sortAttribute,
      hitsPerPage: limit,
      page: page,
    })

    // If device coordinates are provided, add device_distance to each hit
    if (deviceLat !== undefined && deviceLon !== undefined) {
      response.data.hits = response.data.hits.map((hit) => ({
        ...hit,
        device_distance: hit._geo
          ? calculateDistance(
              { lat: deviceLat, lon: deviceLon },
              { lat: hit._geo.lat, lon: hit._geo.lng }
            )
          : null,
      }))
    }

    return response
  },

  searchListingsIndexWithKeywords: async (
    listingKeywords: string,
    amenities: string[] = [],
    sort: string,
    page: number,
    limit: number,
    isAdminSearch: boolean = false,
    lat: number,
    lon: number,
    deviceLat?: number,
    deviceLon?: number
  ) => {
    let sortAttribute: string[] = []

    if (sort.includes('name')) {
      sortAttribute = []
      sortAttribute[0] = 'listing_name:asc'
      sortAttribute[1] = `_geoPoint(${lat}, ${lon}):asc`
    }

    if (sort.includes('rating')) {
      sortAttribute = []
      sortAttribute[0] = 'average_experience_ratings:desc'
      sortAttribute[1] = `_geoPoint(${lat}, ${lon}):asc`
    }

    if (sort.includes('distance')) {
      sortAttribute = []
      sortAttribute[0] = `_geoPoint(${lat}, ${lon}):asc`
    }

    const response = await meilisearchAdmin.post(`/indexes/listings/search`, {
      q: listingKeywords,
      filter:
        (isAdminSearch ? '' : 'listing_is_verified = true AND listing_is_hidden = false') +
        (amenities.length > 0
          ? `${isAdminSearch ? '' : ' AND '}amenities = ${amenities[0]}${amenities
              .map((amenity, index) => (index > 0 ? `amenities = ${amenity}` : ''))
              .join(' AND ')}`
          : ''),
      attributesToRetrieve: ['id', '_geo', 'coordinate'],
      sort: sortAttribute,
      hitsPerPage: limit,
      page: page,
    })

    // If device coordinates are provided, add device_distance to each hit
    if (deviceLat !== undefined && deviceLon !== undefined) {
      response.data.hits = response.data.hits.map((hit) => ({
        ...hit,
        device_distance: hit.coordinate
          ? calculateDistance(
              { lat: deviceLat, lon: deviceLon },
              { lat: hit._geo.lat, lon: hit._geo.lng }
            )
          : null,
      }))
    }

    return response
  },

  searchListingsIndex: async (
    amenities: string[] = [],
    sort: string,
    page: number,
    limit: number,
    isAdminSearch: boolean = false,
    extraQFilter?: Record<string, string>,
    isHidden?: boolean // for admin to filter hidden & published items
  ) => {
    let sortAttribute: string[] = []
    let qFilterString: string = ''

    if (sort.includes('name')) {
      sortAttribute = []
      sortAttribute[0] = 'listing_name:asc'
    }

    if (sort.includes('rating')) {
      sortAttribute = []
      sortAttribute[0] = 'average_experience_ratings:desc'
    }

    if (extraQFilter) {
      const keys = Object.keys(extraQFilter)
      const keysLen = keys.length
      let count = 0
      for (const key of keys) {
        count += 1
        qFilterString += `${extraQFilter[key]}${count >= keysLen ? '' : ','}`
      }
    }

    let fixedFilterString =
      (isAdminSearch
        ? isHidden != undefined
          ? `listing_is_hidden = ${isHidden}`
          : ''
        : 'listing_is_verified = true AND listing_is_hidden = false') +
      (amenities.length > 0
        ? `${isAdminSearch ? (isHidden != undefined ? ' AND ' : '') : ' AND '}amenities = ${
            amenities[0]
          }${amenities
            .map((amenity, index) => (index > 0 ? `amenities = ${amenity}` : ''))
            .join(' AND ')}`
        : '')

    return await meilisearchAdmin.post(`/indexes/listings/search`, {
      q: qFilterString,
      filter: fixedFilterString,
      attributesToRetrieve: ['id', ''],
      sort: sortAttribute,
      hitsPerPage: limit,
      page: page,
    })
  },

  // Indexes
  createIndex: async (data: { uid: string | number; primaryKey: 'id' }) =>
    await meilisearchAdmin.post(`/indexes`, data),
  deleteIndex: async (uid: string | number) => await meilisearchAdmin.delete(`/indexes/${uid}`),

  // Filterable Attributes
  updateFilterableAttributes: async (uid: string | number, data: string[]) =>
    await meilisearchAdmin.put(`/indexes/${uid}/settings/filterable-attributes`, data),

  updateSortableAttributes: async (uid: string | number, data: string[]) =>
    await meilisearchAdmin.put(`/indexes/${uid}/settings/sortable-attributes`, data),

  // Documents
  getDocument: async (uid: string | number, documentId: number | string) =>
    await meilisearchAdmin.get(`/indexes/${uid}/documents/${documentId}`),
  addOrUpdateDocument: async (uid: string | number, data: ({ id: string } & any)[]) =>
    await meilisearchAdmin.put(`/indexes/${uid}/documents`, data),
  deleteDocument: async (uid: string | number, documentId: number | string) =>
    await meilisearchAdmin.delete(`/indexes/${uid}/documents/${documentId}`),
  deleteDocuments: async (uid: string | number, data: (string | number)[]) =>
    await meilisearchAdmin.post(`/indexes/${uid}/documents/delete-batch`, data),
}

const MeilisearchService = {
  search,
  admin,
}

export default MeilisearchService
