import Redis from '@ioc:Adonis/Addons/Redis'
import Logger from '@ioc:Adonis/Core/Logger'
import Listing from 'App/Models/Listing'
import Session from 'App/Models/Session'
import { ListingStatus } from 'Contracts/listing_type'
import { DateTime } from 'luxon'

export const REDIS_KEYS = {
  ACTIVE_LISTINGS: 'active-listings',
  SESSION_HASH: 'active-sessions', // Hash storing all sessions
  SESSION_EXPIRY_SET: 'session-expiry', // Sorted set for session expiry times
  SESSION_IDS: 'session-ids', // Set of active session IDs
  DEVICE_IDS: 'device-ids', // Set of user id mapping to device id
  SESSION_CLEANUP: 'session-cleanup',
  SESSION_ENTRY_CHECK: 'session-entry-check', // Track sessions to join in 5 minutes
  SESSION_ENTRY_EXPIRED: 'session-entry-expired',
} as const

export default class RedisService {
  constructor() {
    Logger.info(`redis subscribe to ${REDIS_KEYS.ACTIVE_LISTINGS}:*`)
    Redis.psubscribe(`${REDIS_KEYS.ACTIVE_LISTINGS}:*`, async (channel, data) => {
      const id = channel.split(':')[1]
      const listing = await Listing.findBy('firestore_id', id)
      if (!listing) {
        return
      }

      const currentStatus =
        data == 'occupied'
          ? ListingStatus.occupied
          : data == 'disinfecting'
          ? ListingStatus.disinfecting
          : ListingStatus.idle

      listing.status = currentStatus
      await listing.save()
    })

    // update session ended that users did not exit
    Logger.info(`redis subscribe to ${REDIS_KEYS.SESSION_CLEANUP}`)
    Redis.subscribe(REDIS_KEYS.SESSION_CLEANUP, async (data) => {
      Logger.info({ data: data }, 'session cleanup')

      // data is expected to be session id
      try {
        const lastSessionByUser = await Session.query()
          .where('id', data)
          .whereNull('actual_ended_at')
          .first()

        if (!lastSessionByUser) {
          return
        }

        lastSessionByUser.actualEndedAt = DateTime.now() //lastSessionByUser.expectedEndedAt
        await lastSessionByUser.save()

        // TODO: do we need to notify? likely the user has already closed the app
        // notify frontend
        // SessionService.emitUpdate(lastSessionByUser.userId, { type: 'session_cleanup' })
        Redis.publish(
          `${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`,
          JSON.stringify({ type: 'session_cleanup' })
        )
      } catch (error) {
        Logger.error(error, data)
      }
    })

    // idle 5 minutes terminate the sessions
    Logger.info(`redis subscribe to ${REDIS_KEYS.SESSION_ENTRY_EXPIRED}`)
    Redis.psubscribe(`${REDIS_KEYS.SESSION_ENTRY_EXPIRED}`, async (channel, data) => {
      // data is expected to be session id
      try {
        const lastSessionByUser = await Session.query()
          .where('id', data)
          .whereNull('actual_ended_at')
          .first()

        if (!lastSessionByUser) {
          return
        }

        lastSessionByUser.actualEndedAt = lastSessionByUser.startedAt
        await lastSessionByUser.save()

        // notify frontend
        // SessionService.emitUpdate(lastSessionByUser.userId, { type: 'entry_expired' })
        Redis.publish(
          `${REDIS_KEYS.SESSION_HASH}:${lastSessionByUser.userId}`,
          JSON.stringify({ type: 'entry_expired' })
        )
      } catch (error) {
        Logger.error(error, channel)
      }
    })
  }
}
