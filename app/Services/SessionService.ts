import { ModelObject } from '@ioc:Adonis/Lucid/Orm'
import Logger from '@ioc:Adonis/Core/Logger'
import Redis from '@ioc:Adonis/Addons/Redis'
import Env from '@ioc:Adonis/Core/Env'
import { REDIS_KEYS } from './RedisService'

export type SessionEvent = {
  type:
  | 'session_created'
  | 'session_extended'
  | 'session_ended'
  | 'active_session'
  | 'no_active_session'
  | 'session_cleanup'
  | 'user_ongoing_session' // user has another ongoing session
  | 'user_ongoing_session_with_restart' // user has another ongoing session but we allow them to restart the session 
  | 'listing_ongoing_session' // listing has another ongoing session
  | 'child_exceed_age' // user's youngest child > 3 years old
  | 'no_child' // user doesn't have any children
  | 'exceed_distance' // user are not within range to unlock the listing
  | 'entry_expired' // user did not join within entry time
} & ModelObject


const ENTRY_TIMEOUT = Env.get('IGLOOHOME_ENTRY_TIMEOUT') * 60 * 1000
export default class SessionService {
  public static async emitUpdate(userId: string, data: SessionEvent) {
    Logger.info('session emit user %s', userId)
    Logger.info('session emit data %o', data)

    if (data.type == 'session_created') {
      console.log('session_created')
      const entryDeadline = Date.now() + ENTRY_TIMEOUT
      const endDeadline = new Date(data.expected_ended_at).getTime()

      await Redis.pipeline()
        .hset(`${REDIS_KEYS.SESSION_HASH}:${userId}`, { ...data, entry_deadline: entryDeadline })
        .hset(REDIS_KEYS.SESSION_IDS, {
          [data.id]: userId,
        })
        .zadd(REDIS_KEYS.SESSION_ENTRY_CHECK, entryDeadline, data.id) // Add to entry check set
        .zadd(REDIS_KEYS.SESSION_EXPIRY_SET, endDeadline, data.id)
        .exec()

      return
    } else if (data.type == 'session_extended') {
      console.log('session_extended', data)

      const userId = await Redis.hget(REDIS_KEYS.SESSION_IDS, data.id)
      if (!userId) return false

      const expiryTimestamp = new Date(data.expected_ended_at).getTime()

      await Redis.pipeline()
        .hset(`${REDIS_KEYS.SESSION_HASH}:${userId}`, { expected_ended_at: data.expected_ended_at })
        .zadd('sessions:expiry', expiryTimestamp, data.id)
        .exec()

      return
    } else if (data.type == 'session_ended') {
      console.log('session_ended', data)

      await Redis.pipeline()
        .del(`${REDIS_KEYS.SESSION_HASH}:${userId}`) // Remove session data
        .hdel(REDIS_KEYS.SESSION_IDS, data.id) // Remove mapping
        .zrem(REDIS_KEYS.SESSION_EXPIRY_SET, data.id) // Remove from expiry set
        .exec()

      return
    }

    await Redis.pipeline()
      /// NOTE: set value to redis for storage
      .hset(`${REDIS_KEYS.SESSION_HASH}:${userId}`, data)
      /// NOTE: publish to trigger pub/sub in gomama-realtime
      .publish(`${REDIS_KEYS.SESSION_HASH}:${userId}`, JSON.stringify(data))
      .exec()
  }
}
