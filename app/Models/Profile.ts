import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, computed, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import EncryptionService from 'App/Services/EncryptionService'
import Logger from '@ioc:Adonis/Core/Logger'

export enum SocialType {
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
  APPLE = 'apple',
  SHOPIFY = 'shopify',
}

export default class Profile extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: string | null

  @column()
  public socialType: SocialType

  @column()
  public socialId: string

  @column()
  public email: string

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public photoUrl: string

  @column()
  public username: string

  @column({ serializeAs: null })
  public encryptedPassword: string | null

  @column({ serializeAs: null })
  public encryptionIv: string | null

  @computed()
  public get password(): string | null {
    return this.getDecryptedPassword()
  }

  public getDecryptedPassword(): string | null {
    try {
      if (!this.encryptedPassword || !this.encryptionIv) {
        return null
      }
      const encryptionService = EncryptionService.getInstance()
      return encryptionService.decrypt(this.encryptedPassword, this.encryptionIv)
    } catch (e) {
      Logger.info(e, 'getDecryptedPassword')
      return null
    }
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async encryptPassword(profile: Profile) {
    if (profile.encryptedPassword) {
      const encryptionService = EncryptionService.getInstance()
      const { encryptedData, iv } = encryptionService.encrypt(profile.encryptedPassword)
      profile.encryptedPassword = encryptedData
      profile.encryptionIv = iv
    }
  }

  // Relationship
  // Which user is the owner of this social profile
  // each user can use multiple social profile to login
  @belongsTo(() => User, { foreignKey: 'userId', localKey: 'id' })
  public user: BelongsTo<typeof User>
}
