import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Session from 'App/Models/Session'
import { isArray } from 'radash'

export default class SessionFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Session, Session>

  public amenities(value: any): void {
    let id = [value]
    if (isArray(value)) {
      id = value
    }

    id.forEach((amenityId) => {
      this.$query.whereHas('listing', (subQuery) => {
        subQuery.whereHas('amenities', (sub2Query) => {
          sub2Query.where('amenity_id', amenityId).andWhere('amenities.is_hidden', false)
        })
      })
    })
  }

  public id(value: string): void {
    this.$query.where('id', 'LIKE', '%' + value + '%')
  }

  public listingId(value: string): void {
    this.$query.where('listing_id', 'LIKE', '%' + value + '%')
  }

  public userId(value: string): void {
    this.$query.where('user_id', 'LIKE', '%' + value + '%')
  }

  public listing(value: string): void {
    this.$query.whereHas('listing', (listingQuery) => {
      listingQuery.whereILike('name', '%' + value + '%')
    })
  }

  public user(value: string): void {
    this.$query.whereHas('user', (userQuery) => {
      userQuery.whereILike('username', '%' + value + '%').orWhereILike('email_address', '%' + value + '%').orWhereILike('first_name', '%' + value + '%').orWhereILike('last_name', '%' + value + '%')
    })
  }
}
