import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import _ from 'lodash'
import Env from '@ioc:Adonis/Core/Env'

const requestHandler = (request: InternalAxiosRequestConfig) => {

  return request
}

const successHandler = (response: AxiosResponse) => {
  return response
}

const errorHandler = async (error: AxiosError<any>) => {

  return Promise.reject({
    message:
      error.response?.data?.errorCode ??
      error.response?.data?.message ??
      error.response?.data?.errors ??
      error.response?.data?.code ??
      error.response ??
      error,
  })
}

export const zestReviewSuperAdmin = axios.create({
  baseURL: Env.get('ZEST_REVIEW_BASE_URL'),
  headers: {
    'x-api-key': Env.get('ZEST_REVIEW_API_KEY'),
  },
})

const zestReviewAdmin = axios.create({
  baseURL: Env.get('ZEST_REVIEW_BASE_URL'),
  headers: {
    'Authorization': `Bearer ${Env.get('ZEST_REVIEW_ACCESS_TOKEN')}`,
  },
})
zestReviewAdmin.interceptors.request.use(requestHandler)
zestReviewAdmin.interceptors.response.use(
  (response) => successHandler(response),
  (error) => errorHandler(error)
)

export default zestReviewAdmin
