import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import _ from 'lodash'
import Env from '@ioc:Adonis/Core/Env'

const requestHandler = (request: InternalAxiosRequestConfig) => {

  return request
}

const successHandler = (response: AxiosResponse) => {
  return response
}

const errorHandler = async (error: AxiosError<any>) => {

  return Promise.reject({
    message:
      error.response?.data?.errorCode ??
      error.response?.data?.message ??
      error.response?.data?.errors ??
      error.response?.data?.code ??
      error.response ??
      error,
  })
}

export const zestLoyaltySuperAdmin = axios.create({
  baseURL: Env.get('ZEST_LOYALTY_BASE_URL'),
  headers: {
    'x-api-key': Env.get('ZEST_LOYALTY_API_KEY'),
  },
})

const zestLoyaltyPublicAdmin = axios.create({
  baseURL: Env.get('ZEST_LOYALTY_BASE_URL'),
  headers: {
    'Authorization': 'Bearer ' + Env.get('ZEST_LOYALTY_ACCESS_TOKEN'),
  },
})
zestLoyaltyPublicAdmin.interceptors.request.use(requestHandler)
zestLoyaltyPublicAdmin.interceptors.response.use(
  (response) => successHandler(response),
  (error) => errorHandler(error)
)

export default zestLoyaltyPublicAdmin
