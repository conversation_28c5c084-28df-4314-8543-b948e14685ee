import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import ListingFlag from 'App/Models/ListingFlag'
import { ListingFlagAction, ListingFlagCategory } from 'Contracts/listing_flag_type'
import Logger from '@ioc:Adonis/Core/Logger'
import Listing from 'App/Models/Listing'
import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'
import {
  disk_name,
  generateRandomCode,
  imageFileSpec,
  remotePathListingFlag,
  varcharRule,
} from 'App/utils'
import _ from 'lodash'

export default class ListingFlagsController {
  // Normal User Routes
  // C
  public async createListingFlag({
    request,
    response,
    params: { listing_id },
    auth,
  }: HttpContextContract) {
    const listing = await Listing.find(listing_id)
    if (!listing) {
      return response.notFound({ success: false, message: 'Listing not found' })
    }

    const user = await auth.authenticate() // Expect all type of user to raise a flag for a listing.

    try {
      const validationSchema = schema.create({
        category: schema.enum(Object.values(ListingFlagCategory)),
        reason: schema.string.optional(varcharRule),
        reference_image_files: schema.array
          .optional([rules.minLength(1), rules.maxLength(3)])
          .members(schema.file(imageFileSpec)),
      })

      const isExist = await ListingFlag.query()
        .where('listing_id', listing_id)
        .andWhere('user_id', user.id)
        .andWhereNull('reviewed_at')
        .first()

      if (isExist) {
        return response.forbidden({
          success: false,
          message:
            'A flag for this listing already exist and is not yet reviewed by an admin, can only raise one flag for a listing at a time.',
        })
      }

      const { reference_image_files, ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const newListingFlag = await ListingFlag.create({
        ...validatedData,
        listingId: listing.id,
        userId: user.id,
      })

      if (reference_image_files && reference_image_files.length > 0) {
        const imageFilePromises = reference_image_files.map(async (reference_image_file) => {
          const remoteName = `${listing_id}_${generateRandomCode(10)}.${
            reference_image_file.extname
          }`
          await reference_image_file.moveToDisk(
            remotePathListingFlag,
            { name: remoteName },
            disk_name
          )
          return remoteName
        })

        const imageFileNames = await Promise.all(imageFilePromises)

        newListingFlag.referenceImages = imageFileNames
        await newListingFlag.save()
      }

      return response.ok({
        success: true,
        message: 'Created listing flag successfully',
        data: newListingFlag,
      })
    } catch (err) {
      console.log(err)
      Logger.error(err, 'createListingFlag')
      return response.badRequest(err)
    }
  }

  // R
  public async retrieveListingFlag({ response, params: { id }, auth }: HttpContextContract) {
    const user = await auth.authenticate() // user

    try {
      const listingFlag = await ListingFlag.query()
        .where('id', id)
        .andWhere('user_id', user.id)
        .first()

      if (!listingFlag) {
        return response.notFound({ success: false, message: 'Listing Flag not found' })
      }

      return response.ok({ data: listingFlag })
    } catch (err) {
      Logger.error(err, 'retrieveListingFlag')
      return response.internalServerError(err)
    }
  }

  public async retrieveListingFlags({ request, response, auth }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'id:desc').split(':')
    const user = await auth.authenticate() // user

    try {
      const listingFlags = await ListingFlag.query()
        .where('user_id', user.id)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(listingFlags)
    } catch (err) {
      Logger.error(err, 'retrieveAllListingFlagUser')
      return response.internalServerError(err)
    }
  }

  // Admin Routes

  // R
  public async retrieveListingFlagAdmin({ response, params: { id } }: HttpContextContract) {
    try {
      const listingFlag = await ListingFlag.find(id)

      if (!listingFlag) {
        return response.notFound({ success: false, message: 'Listing Flag not found' })
      }

      return response.ok({ data: listingFlag })
    } catch (err) {
      Logger.error(err, 'retrieveListingFlagUser')
      return response.internalServerError(err)
    }
  }

  public async retrieveAllListingFlagsAdmin({ request, response }: HttpContextContract) {
    const page = request.input('page', 1)
    const limit = request.input('limit', 20)
    const sort = request.input('sort', 'created_at:desc').split(':')
    const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

    try {
      const listingFlags = await ListingFlag.filter(filters)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(listingFlags)
    } catch (err) {
      Logger.error(err, 'retrieveAllListingFlagAdmin')
      return response.internalServerError(err)
    }
  }

  // U
  public async updateListingFlagAdmin({
    request,
    response,
    params: { id },
    auth,
  }: HttpContextContract) {
    Logger.info(request.all(), 'updateListingFlagAdmin')
    const user = await auth.authenticate() // Admin, this route mainly is to update review result (reviewed_at, action and action reason), the rest of the field is provided just in case needed.
    const listingFlag = await ListingFlag.find(id)
    const trx = await Database.transaction()

    if (!listingFlag) {
      await trx.rollback()
      return response.notFound({ success: false, message: 'Listing flag not found' })
    }

    if (listingFlag.reviewedAt) {
      await trx.rollback()
      return response.forbidden({
        success: false,
        message: 'The Listing Flag has been reviewed, cannot be updated',
      })
    }
    const validationSchema = schema.create({
      user_id: schema.string.optional([rules.exists({ column: 'id', table: 'users' })]),
      listing_id: schema.string.optional([rules.exists({ column: 'id', table: 'listings' })]),
      category: schema.enum.optional(Object.values(ListingFlagCategory)),
      reason: schema.string.optional(varcharRule),
      action: schema.enum.optional(Object.values(ListingFlagAction)),
      action_reason: schema.string.optional(varcharRule.concat([rules.requiredIfExists('action')])),
    })
    const validatedData = await request.validate({ schema: validationSchema })

    try {
      const updateFlag = await listingFlag
        .merge({
          ...validatedData,
          reviewedAt: validatedData.action ? DateTime.now() : undefined,
          actionBy: validatedData.action ? user.id : undefined, // if action has value, means it is a flag review
        })
        .useTransaction(trx)
        .save()

      if (
        updateFlag.reviewedAt &&
        updateFlag.action == ListingFlagAction.hideListing // Listing is hidden based on this review, the rest of the flag with same listing ID is set to reviewed as well.
      ) {
        const restOfTheSameFlagListing = await ListingFlag.query()
          .where('listing_id', updateFlag.listingId)
          .andWhereNull('reviewed_at')
          .andWhereNot('id', id)

        // Action is hide listing, hide the mentioned listing
        const mentionedListing = await Listing.findOrFail(updateFlag.listingId)

        mentionedListing.isHidden = true
        await mentionedListing.useTransaction(trx).save()

        updateFlag.actionBy = user.id
        updateFlag.reviewedAt = DateTime.now()
        await updateFlag.useTransaction(trx).save()

        // single query to update many
        await ListingFlag.updateOrCreateMany(
          'id',
          restOfTheSameFlagListing.map((flag) => {
            return {
              id: flag.id,
              reviewedAt: DateTime.now(),
              action: ListingFlagAction.hideListing,
              actionReason:
                'This listing flag has not been reviewed, but the mentioned listing is already hidden based on flag report id: ' +
                listingFlag.id,
              actionBy: user.id,
            }
          }),
          { client: trx }
        )

        await trx.commit()

        return response.ok({ success: true, message: 'Listing flag updated successfully' })
      }

      await trx.commit()

      return response.ok({ success: true, message: 'Listing flag updated successfully' })
    } catch (err) {
      await trx.rollback()
      Logger.error(err, 'updateListingFlagAdmin')
      return response.internalServerError(err)
    }
  }

  // D
  public async deleteListingFlag({ response, params: { id } }: HttpContextContract) {
    const listingFlag = await ListingFlag.find(id)
    if (!listingFlag) {
      return response.notFound({ success: false, message: 'Listing flag not found' })
    }

    try {
      await listingFlag.delete()
      return response.ok({ success: true, message: 'Listing flag deleted successfully' })
    } catch (err) {
      Logger.error(err, 'deleteListingFlag')
      return response.internalServerError(err)
    }
  }
}
