import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import User from 'App/Models/User'
import {
  cipherDecryption,
  cipherEncryption,
  generateCipherKey,
  generateJWKThumbprint,
  generateClientAssertion,
  generatePkcePairValue,
  generateRandomCode,
  generateEphemeralKeyPair,
  getSingpassConfig,
  serializeEncodeURI,
  generateDPoP,
} from 'App/utils'
import axios, { AxiosResponse } from 'axios'

const singpassTokenEndpoint = async (
  authorizationCode: string,
  clientAssertion: string,
  codeVerifier: string,
  dpop: string
) => {
  const { clientId, grantType, myinfoUrl, redirectUri } = getSingpassConfig()
  const cacheCtl = 'no-cache'
  const contentType = 'application/x-www-form-urlencoded'
  const requestHeaders = {
    'Content-Type': contentType,
    'Cache-Control': cacheCtl,
    'DPoP': dpop,
  }

  // const headers =

  const assertionType = 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer'
  const bodyParams = new URLSearchParams()
  bodyParams.append('client_id', clientId)
  bodyParams.append('redirect_uri', redirectUri)
  bodyParams.append('grant_type', grantType)
  bodyParams.append('code', authorizationCode)
  bodyParams.append('client_assertion_type', assertionType)
  bodyParams.append('client_assertion', clientAssertion)
  bodyParams.append('code_verifier', codeVerifier)

  return await axios.post(myinfoUrl, bodyParams, {
    headers: requestHeaders,
  })
}

export default class SingpassV4AuthsController {
  public async initializeSingpassOAuth({ response, auth }: HttpContextContract) {
    try {
      // 1. Retrieve user from auth middleware
      console.info('👶 1 of 5: Retrieve user from auth middleware.')
      const user = await User.findOrFail(auth.user?.id ?? 1)

      // 2. Generate nonce, state and secret random strings.
      // Re-generate on every auth session.
      console.info('👶 2 of 5: Generate random nonce, state and secret strings.')
      const nonce = generateRandomCode(255)
      const state = generateRandomCode(255)
      const secret = generateRandomCode(255)
      const [codeVerifier, codeChallenge, codeChallengeMethod] = generatePkcePairValue()
      const [cipherSecret, cipherIv] = generateCipherKey()

      const encryptedCodeVerifier = cipherEncryption(codeVerifier, cipherSecret, cipherIv)

      // 3. User id exists and valid and update random nonce, state and secret strings.
      console.info(
        '👶 3 of 5: User id exists and valid and update random nonce, state and secret strings.'
      )
      await Database.transaction(async (trx) => {
        user.singpassNonce = nonce
        user.singpassState = state
        user.singpassSecret = secret
        user.cipherSecret = cipherSecret
        user.cipherIv = cipherIv
        user.codeVerifier = encryptedCodeVerifier
        user.useTransaction(trx)
        await user.save()
      })

      // 4. Assign individual Singpass private variables for PKI authorization.
      console.info('👶 4 of 5: Assign individual Singpass private variables for PKI authorization.')
      const { clientId, scope, purpose, myinfoUrl, redirectUri } = getSingpassConfig()
      const myinfoAuthoriseUrl = `${myinfoUrl}/authorize`

      // 5. Generate full Singpass Myinfo authorise url.
      console.info('👶 5 of 5: Generate full Singpass Myinfo authorise url.')
      const queryParams = {
        clientId,
        scope: scope.replace(/,/g, ' '),
        purpose_id: purpose,
        codeChallenge,
        codeChallengeMethod,
        redirectUri,
        responseType: 'code',
      }
      const myinfoAuthoriseFullUrl = serializeEncodeURI(myinfoAuthoriseUrl, queryParams)

      return response.ok({
        success: myinfoAuthoriseFullUrl ? true : false,
        data: myinfoAuthoriseFullUrl,
      })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  public async singpassMyInfoCallback({ request, response, auth }: HttpContextContract) {
    try {
      // 1. Retrieve user from auth middleware.
      console.info('👶 1 of 17: Retrieve user from auth middleware.')
      const user = await User.findOrFail(auth.user?.id ?? 1)

      // 2. Retrieve and validate the user's singpass authorization state with the state from mobile app.
      console.info(
        "👶 2 of 17: Retrieve and validate the user's singpass authorization state with the state from mobile app."
      )
      // const validationSchema = schema.create({
      //   authorization_code: schema.string(),
      //   authorization_state: schema.string([
      //     rules.exists({ table: 'users', column: 'singpass_state' }),
      //   ]),
      // })
      // const validationData = await request.validate({ schema: validationSchema })
      const authorizationCode = request.input('code')
      // const { authorization_code: authorizationCode, authorization_state: authorizationState } =
      //   validationData

      if (!authorizationCode) {
        return response.badRequest({
          success: false,
        })
      }

      // 3. Prepare Client JWT Assertion and code verifier.
      console.info('👶 3 of 17: Prepare Client JWT Assertion and code verifier.')
      const {
        clientId,
        // clientSecret,
        // scope,
        // grantType,
        myinfoUrl,
        // redirectUri,
        // pkiMyInfoPublicCert,
        pkiCompanyPrivateKey,
      } = getSingpassConfig()
      const myinfoTokenUrl = `${myinfoUrl}/token`

      const ephemeralKeyPair = await generateEphemeralKeyPair()
      const jwkThumbprint = await generateJWKThumbprint(ephemeralKeyPair.publicKey)
      const jwtAssertion = await generateClientAssertion({
        alg: 'ES256',
        clientId,
        aud: 'https://id.singpass.gov.sg',
        privateSigningKey: pkiCompanyPrivateKey,
        jktThumbprint: jwkThumbprint,
      })
      const dpop = await generateDPoP(myinfoTokenUrl, 'POST', ephemeralKeyPair)
      const codeVerifier = cipherDecryption(user.codeVerifier, user.cipherSecret, user.cipherIv)

      // 7. Call Singpass Myinfo token endpoint to retrieve authorization tokens.
      console.info(
        '👶 7 of 17: Call Singpass Myinfo token endpoint to retrieve authorization tokens.'
      )
      let tokenResponse: AxiosResponse | undefined
      try {
        tokenResponse = await singpassTokenEndpoint(
          authorizationCode,
          jwtAssertion,
          codeVerifier,
          dpop
        )
      } catch (e) {
        console.error(
          '❌ 10 of 17: Call Singpass Myinfo token endpoint to retrieve authorization tokens.',
          e
        )
      }

      // 12. Verify JWS access token with Singpass Myinfo public certificate.
      console.info('👶 8 of 17: Verify JWS access token with Singpass Myinfo public certificate.')
      const accessToken = tokenResponse?.data.access_token ?? ''
      console.log(accessToken)
      // const decoded = utils.verifyJWS(accessToken, myinfoPublicCertAccessPayload);
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }
}
