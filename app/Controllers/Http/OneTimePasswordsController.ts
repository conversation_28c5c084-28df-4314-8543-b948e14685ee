import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import User, { userFindFields } from 'App/Models/User'
import Logger from '@ioc:Adonis/Core/Logger'
import Event from '@ioc:Adonis/Core/Event'
import Database from '@ioc:Adonis/Lucid/Database'
import { render } from '@react-email/render'
import { DateTime } from 'luxon'
import { OtpStatus, OtpType } from 'Contracts/otp_type'
import OneTimePassword from 'App/Models/OneTimePassword'
import { dialCodeToCountryCodeAndName, generateOtp, validatePhoneNumber } from 'App/utils'
import { Email } from '../../../resources/components/Email'
import { createShopifyCustomerIfNotExists } from './AuthController'

export const OTP_DURATION = 5 //OTP Duration in minutes

export default class OneTimePasswordsController {
  public async createOtpLoginOrRegister({ response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      login_account: schema.string([rules.maxLength(255), rules.minLength(1)]),
    })

    const payload = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isEmail = emailPattern.test(payload.login_account)
    const isPhone = validatePhoneNumber(payload.login_account)

    if (!isEmail && !isPhone) {
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })
    }

    const existingUserEmail = await User.findBy('email_address', payload.login_account) //Check if email is already registered
    const existingUserPhone = await User.query()
      .apply((scopes) => scopes.queryComparefullPhoneNumber(payload.login_account))
      .first()
    const otp = generateOtp(6) + ''
    const newExpTime = DateTime.now().plus({ minutes: OTP_DURATION })
    const otpType = existingUserEmail || existingUserPhone ? OtpType.login : OtpType.registration

    Logger.info(
      {
        login_account: payload.login_account,
        otp,
        otpType,
      },
      'createOtpLoginOrRegister'
    )
    if (existingUserEmail || existingUserPhone) {
      // Login OTP
      // Email is existing, proceed with generate login OTP

      try {
        if (isEmail) {
          const htmlRendered = render(
            Email({ otp: otp, expiresAt: newExpTime, type: 'login', duration: OTP_DURATION })
          )
          const plainText = await render(
            Email({ otp: otp, expiresAt: newExpTime, type: 'login', duration: OTP_DURATION }),
            {
              plainText: true,
            }
          )
          await Event.emit('new:otp-email', {
            from: process.env.SES_SENDER!,
            to: payload.login_account,
            subject: 'Go!Mama Account Login OTP',
            html: htmlRendered,
            text: plainText,
          })
          //Create new login OTP for an email
          await OneTimePassword.create({
            email: payload.login_account,
            oneTimePassword: otp,
            expiresAt: newExpTime,
            type: otpType,
          })
        }

        if (isPhone) {
          await Event.emit('new:otp-phone', {
            to: payload.login_account,
            phoneMessage: 'Your Go!Mama OTP is ' + otp,
          })
          //Create new login OTP for a phone
          await OneTimePassword.create({
            phone: payload.login_account,
            oneTimePassword: otp,
            expiresAt: newExpTime,
            type: otpType,
          })
        }

        return response.created({
          success: true,
          message: `${isEmail ? 'Email' : 'Phone SMS'} Login OTP created successfully`,
          data: {
            is_login: true,
          },
        })
      } catch (err) {
        Logger.error(err, 'createOtpLoginOrRegister')
        return response.badRequest(err)
      }
    } else {
      // Register OTP
      // Email registration OTP
      if (isEmail && existingUserEmail) {
        //Is an existing email, return conflict 409 error
        return response.conflict({ message: 'The email is already in use' })
      }
      //Phone registration OTP
      if (isPhone && existingUserPhone) {
        //Is an existing phone, return conflict 409 error
        return response.conflict({ message: 'The phone is already in use' })
      }

      //Email/Phone is new, proceed with generate OTP
      try {
        if (isEmail) {
          const htmlRendered = render(
            Email({ otp: otp, expiresAt: newExpTime, type: 'registration', duration: OTP_DURATION })
          )
          const plainText = await render(
            Email({
              otp: otp,
              expiresAt: newExpTime,
              type: 'registration',
              duration: OTP_DURATION,
            }),
            {
              plainText: true,
            }
          )
          await Event.emit('new:otp-email', {
            from: process.env.SES_SENDER!,
            to: payload.login_account,
            subject: 'Go!Mama Account Registration OTP',
            html: htmlRendered,
            text: plainText,
          })

          //Create new registration OTP for an email
          await OneTimePassword.create({
            email: payload.login_account,
            oneTimePassword: otp,
            expiresAt: newExpTime,
            type: otpType,
          })
        }

        if (isPhone) {
          await Event.emit('new:otp-phone', {
            to: payload.login_account,
            phoneMessage: 'Your Go!Mama OTP is ' + otp,
          })
          //Create new registration OTP for a phone
          await OneTimePassword.create({
            phone: payload.login_account,
            oneTimePassword: otp,
            expiresAt: newExpTime,
            type: otpType,
          })
        }

        return response.created({
          success: true,
          message: `${isEmail ? 'Email' : 'Phone SMS'} Registration OTP created successfully`,
          data: {
            is_login: false,
          },
        })
      } catch (err) {
        Logger.error(err, 'createOtpLoginOrRegister')
        return response.badRequest(err)
      }
    }
  }

  public async matchOtpRegistration({ response, request }: HttpContextContract) {
    Logger.info(request.all(), 'matchOtpRegistration')

    const validationSchema = schema.create({
      register_account: schema.string({ trim: true }, [rules.minLength(1), rules.maxLength(255)]),
      otp: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isEmail = emailPattern.test(validationData.register_account)
    const isPhone = validatePhoneNumber(validationData.register_account)

    if (!isEmail && !isPhone) {
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })
    }

    const userLatestRegisterOtp = await OneTimePassword.query()
      .where('type', 'registration')
      .andWhere('status', 'pending')
      .andWhere('expires_at', '>=', DateTime.now().toSQL())
      .if(isEmail, (query) => query.where('email', validationData.register_account))
      .if(isPhone, (query) => query.where('phone', validationData.register_account))
      .orderBy('created_at', 'desc')
      .first()

    if (!userLatestRegisterOtp) {
      return response.unauthorized({
        success: false,
        message: 'OTP expired or not requested, please request a new OTP for register',
      })
    }

    // if (userLatestRegisterOtp && userLatestRegisterOtp.expiresAt < DateTime.now()) {
    //   return response.badRequest({ success: false, message: 'The latest OTP has expired' })
    // }

    if (userLatestRegisterOtp.oneTimePassword != validationData.otp) {
      return response.unauthorized({ success: false, message: 'The OTP does not match' })
    }

    userLatestRegisterOtp.status = OtpStatus.verified

    await userLatestRegisterOtp.save()

    return response.ok({ success: true, message: 'Registration OTP matched successfully.' })
  }

  public async createOtpUpdateEmailOrPhone({ response, request, auth }: HttpContextContract) {
    const user = await auth.authenticate()

    const validationSchema = schema.create({
      mobile_or_email: schema.string([rules.maxLength(255), rules.minLength(1)]),
    })

    const { mobile_or_email } = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isEmail = emailPattern.test(mobile_or_email)
    const isPhone = validatePhoneNumber(mobile_or_email)

    if (!isEmail && !isPhone) {
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })
    }

    try {
      const otp = generateOtp(6) + ''
      const newExpTime = DateTime.now().plus({ minutes: OTP_DURATION })
      const otpType = isEmail ? OtpType.updateEmail : OtpType.updatePhone

      Logger.info(
        {
          mobile_or_email,
          otp,
          otpType,
        },
        'createOtpUpdateEmailOrPhone'
      )

      if (isEmail) {
        const isExist = await User.findBy('email_address', mobile_or_email)

        if (user.emailAddress == mobile_or_email && user.isEmailAddressVerified) {
          return response.badRequest({
            success: false,
            message: 'Please provide a different email than your current email',
          })
        }

        // if an email is exist, and that firestoreId is null, which indicates is new user in new backend, then allow verify, this is because old system might have email that hasn't been verify yet.
        if (isExist && !isExist.firestoreId) {
          return response.conflict({ success: false, message: 'Email already exist' })
        }

        const htmlRendered = render(
          Email({ otp: otp, expiresAt: newExpTime, type: 'update_email', duration: OTP_DURATION })
        )
        const plainText = render(
          Email({ otp: otp, expiresAt: newExpTime, type: 'update_email', duration: OTP_DURATION }),
          { plainText: true }
        )
        await Event.emit('new:otp-email', {
          from: process.env.SES_SENDER!,
          to: mobile_or_email,
          subject: 'Go!Mama Account Email Update OTP',
          html: htmlRendered,
          text: plainText,
        })

        //Create new registration OTP for an email
        await OneTimePassword.create({
          email: mobile_or_email,
          oneTimePassword: otp,
          expiresAt: newExpTime,
          type: otpType,
        })
      } else {
        if (isPhone) {
          const isExist = await User.query()
            .whereRaw("CONCAT('+', country_dial_code, ' ', mobile_number) = ?", [mobile_or_email])
            .first()

          if (user.mobileNumber == mobile_or_email && user.isMobileNumberVerified) {
            return response.badRequest({
              success: false,
              message: 'Please provide a different mobile number than your current mobile number',
            })
          }

          // if an email is exist, and that firestoreId is null, which indicates is new user in new backend, then allow verify, this is because old system might have email that hasn't been verify yet.
          if (isExist && !isExist.firestoreId) {
            return response.conflict({ success: false, message: 'Mobile number already exist' })
          }

          await Event.emit('new:otp-phone', {
            to: mobile_or_email,
            phoneMessage: 'Your Go!Mama OTP to update mobile number is ' + otp,
          })
          //Create new login OTP for a phone
          await OneTimePassword.create({
            phone: mobile_or_email,
            oneTimePassword: otp,
            expiresAt: newExpTime,
            type: otpType,
          })
        }
      }
      return response.ok({
        success: true,
        message: `Successfully created otp for ${isEmail ? 'email' : 'phone'} update`,
      })
    } catch (err) {
      Logger.error(err, 'createOtpUpdateEmailOrPhone')
      return response.badRequest(err)
    }
  }

  public async matchOtpUpdateEmailOrPhone({ response, request, auth }: HttpContextContract) {
    Logger.info(request.all(), 'matchOtpUpdateEmailOrPhone')

    const user = await auth.authenticate()

    const trx = await Database.transaction()

    const validationSchema = schema.create({
      country_dial_code: schema.string.optional([
        rules.requiredIfExists('new_mobile_number'),
        rules.maxLength(7),
        rules.minLength(1),
      ]), // max length 4 because + sign and longest dial code is 3 digit long
      new_mobile_number: schema.string.optional([
        rules.requiredIfExists('country_dial_code'),
        rules.maxLength(20),
      ]),
      new_email: schema.string.optional([rules.maxLength(255), rules.minLength(1)]),
      otp: schema.string({ trim: true }, []),
    })

    const { new_email, new_mobile_number, country_dial_code, otp } = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const fullMobileNumber =
      country_dial_code != null && new_mobile_number != null
        ? country_dial_code.trim() + ' ' + new_mobile_number.trim()
        : null
    const isEmail = new_email != null && emailPattern.test(new_email)
    const isPhone = fullMobileNumber != null && validatePhoneNumber(fullMobileNumber)

    if (!isEmail && !isPhone) {
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })
    }

    const isExist = await User.query()
      .if(isEmail, (query) => query.where('email_address', new_email!))
      .if(isPhone, (query) =>
        query
          .where('mobile_number', new_mobile_number!)
          .andWhere('country_dial_code', country_dial_code!)
      )
      .first()

    if (isExist && !isExist.firestoreId) {
      return response.conflict({
        success: false,
        message: `${isEmail ? 'Email' : 'Phone number'} already registered as an existing user.`,
      })
    }

    try {
      let updatedUser: User
      if (isEmail) {
        const userLatestUpdateEmailOrPhoneOtp = await OneTimePassword.query()
          .where('type', OtpType.updateEmail)
          .andWhere('status', 'pending')
          .andWhere('expires_at', '>=', DateTime.now().toSQL())
          .andWhere('email', new_email)
          .orderBy('created_at', 'desc')
          .first()
        if (!userLatestUpdateEmailOrPhoneOtp) {
          return response.unauthorized({
            success: false,
            message: 'OTP expired or not requested, please request a new OTP for email update',
          })
        }

        // if (
        //   userLatestUpdateEmailOrPhoneOtp &&
        //   userLatestUpdateEmailOrPhoneOtp.expiresAt < DateTime.now()
        // ) {
        //   return response.badRequest({ success: false, message: 'The latest OTP has expired' })
        // }

        if (
          userLatestUpdateEmailOrPhoneOtp &&
          userLatestUpdateEmailOrPhoneOtp.oneTimePassword != otp
        ) {
          return response.unauthorized({ success: false, message: 'The OTP does not match' })
        }

        userLatestUpdateEmailOrPhoneOtp.status = OtpStatus.verified

        user.emailAddress = new_email

        user.isEmailAddressVerified = true

        updatedUser = await user.useTransaction(trx).save()

        await userLatestUpdateEmailOrPhoneOtp.useTransaction(trx).save()

        await trx.commit()

        // NOTE: wait until trx is completed to create shopify customer
        await createShopifyCustomerIfNotExists(updatedUser)
      } else {
        const userLatestUpdateEmailOrPhoneOtp = await OneTimePassword.query()
          .where('type', OtpType.updatePhone)
          .andWhere('status', 'pending')
          .andWhere('expires_at', '>=', DateTime.now().toSQL())
          .andWhere('phone', fullMobileNumber!)
          .orderBy('created_at', 'desc')
          .first()

        if (!userLatestUpdateEmailOrPhoneOtp) {
          return response.unauthorized({
            success: false,
            message: 'OTP expired or not requested, please request a new OTP for phone update',
          })
        }

        if (
          userLatestUpdateEmailOrPhoneOtp &&
          userLatestUpdateEmailOrPhoneOtp.oneTimePassword != otp
        ) {
          return response.unauthorized({ success: false, message: 'The OTP does not match' })
        }

        userLatestUpdateEmailOrPhoneOtp.status = OtpStatus.verified

        const countryCodeAndName = dialCodeToCountryCodeAndName(fullMobileNumber?.trim()!)
        if (countryCodeAndName) {
          user.countryCode = countryCodeAndName.countryCode
          user.countryName = countryCodeAndName.countryName
        }
        user.countryDialCode = country_dial_code!.startsWith('+')
          ? country_dial_code!.trim().slice(1)
          : country_dial_code!.trim()
        user.mobileNumber = new_mobile_number!.trim()
        user.isMobileNumberVerified = true

        updatedUser = await user.useTransaction(trx).save()

        await userLatestUpdateEmailOrPhoneOtp.useTransaction(trx).save()

        await trx.commit()
      }

      return response.ok({
        success: true,
        message: `New ${isEmail ? 'email' : 'phone'} validation OTP matched successfully.`,
        data: updatedUser.serialize(userFindFields),
      })
    } catch (err) {
      Logger.error(err, 'matchOtpUpdateEmailOrPhone')
      return response.badRequest(err)
    }
  }
}
