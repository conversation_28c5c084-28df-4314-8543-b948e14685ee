import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import ListingRating from 'App/Models/ListingRating'
import Session from 'App/Models/Session'
import { statementFilter } from 'App/utils/statementFilter'
import _ from 'lodash'
import Logger from '@ioc:Adonis/Core/Logger'
import Listing from 'App/Models/Listing'
import { DateTime } from 'luxon'
import Event from '@ioc:Adonis/Core/Event'

export default class ListingRatingsController {
  // C
  public async createListingRating({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'createListingRating')
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        listing_id: schema.string.optional([
          rules.exists({ column: 'id', table: 'listings' }),
          rules.requiredIfNotExists('session_id'),
        ]),
        session_id: schema.string.optional([
          rules.exists({ column: 'id', table: 'sessions' }),
          rules.requiredIfNotExists('listing_id'),
        ]),
        app_rating: schema.number([rules.range(1, 5)]),
        experience_rating: schema.number([rules.range(1, 5)]),
        listing_rating: schema.number([rules.range(1, 5)]),
        review: schema.string.optional(),
      })

      let { review, listing_id, session_id, ...validationData } = await request.validate({
        schema: validationSchema,
      })

      let listingRating: ListingRating | null = null

      if (session_id) {
        // Gomama Listing Session Rating
        const session = await Session.findOrFail(session_id)

        if (session.userId != user.id) {
          return response.forbidden({
            success: false,
            message: 'The session does not belongs to user',
          })
        }

        if (!session.actualEndedAt) {
          return response.forbidden({
            success: false,
            message: 'The session is not ended yet',
          })
        }

        if (session.startedAt.equals(session.actualEndedAt)) {
          return response.forbidden({
            success: false,
            message: 'Cannot create listing rating for a no entry session',
          })
        }

        const maxReviewDay = 2
        if (Math.abs(session.createdAt.diffNow('days').days) > maxReviewDay) {
          // Note: This should not happen because we expect cron job will automatically create a rating with 5 stars for all experience and no review message.
          return response.forbidden({
            success: false,
            message: `The session can only be rated manually within ${maxReviewDay} days`,
          })
        }

        const isExist = await ListingRating.query()
          .where('listing_id', session.listingId)
          .andWhere('session_id', session.id)
          .first()

        if (isExist) {
          return response.conflict({
            success: false,
            message: 'An existing rating is already assigned to the session',
          })
        }

        listingRating = await Database.transaction(async (trx) => {
          review = review && statementFilter(review)
          const newListingRating = await ListingRating.create(
            {
              ...validationData,
              listingId: session.listingId,
              sessionId: session_id,
              review: review,
            },
            { client: trx }
          )
          return newListingRating
        })

        // retrieve listing relation
        if (listingRating.id) {
          await listingRating.load('listing')
        }
      } else if (listing_id) {
        // Public nursing room Listing Rating
        const listing = await Listing.findOrFail(listing_id)

        if (listing.lockId) {
          return response.forbidden({
            success: false,
            message: 'Listing with lock should have a session id to submit a review.',
          })
        }

        // Prevent Spam, one rating only for same listing in a day
        const latestReviewSameListing = await ListingRating.query()
          .where('listing_id', listing_id)
          .orderBy('created_at', 'desc')
          .first()

        if (
          latestReviewSameListing &&
          latestReviewSameListing.createdAt.hasSame(DateTime.now(), 'day')
        ) {
          return response.forbidden({
            success: false,
            message: 'Each listing can only receive rating review once a day.',
          })
        }

        listingRating = await Database.transaction(async (trx) => {
          review = review && statementFilter(review)
          const newListingRating = await ListingRating.create(
            { ...validationData, listingId: listing.id, review: review },
            { client: trx }
          )
          return newListingRating
        })
      }

      Event.emit('sync:meilisearch', { type: 'update', listingId: listingRating!.listingId }) //Sync Meili with latest update

      return response.ok({
        success: listingRating?.id ? true : false,
        message: listingRating?.id
          ? 'Created listing rating successfully'
          : 'Failed to create listing rating, please try again',
        data: listingRating,
      })
    } catch (error) {
      Logger.error(error, 'createListingRating')
      return response.badRequest(error)
    }
  }

  // R
  public async findListingRatingSummary({ params, response }: HttpContextContract) {
    try {
      const listingRatingSummary = await Listing.query()
        .where('id', params.id)
        .withAggregate('listingRatings', (query) => {
          query.avg('experience_rating').as('average_experience_ratings')
        })
        .withCount('listingRatings', (query) => {
          query.count('*').as('total_experience_ratings')
        })
        .withCount('listingRatings', (query) => {
          query.where('experience_rating', 5).count('*').as('five_stars_count')
        })
        .withCount('listingRatings', (query) => {
          query.where('experience_rating', 4).count('*').as('four_stars_count')
        })
        .withCount('listingRatings', (query) => {
          query.where('experience_rating', 3).count('*').as('three_stars_count')
        })
        .withCount('listingRatings', (query) => {
          query.where('experience_rating', 2).count('*').as('two_stars_count')
        })
        .withCount('listingRatings', (query) => {
          query.where('experience_rating', 1).count('*').as('one_star_count')
        })
        .withCount('sessions', (query) => {
          query.count('*').as('total_sessions')
        })
        .first()

      if (!listingRatingSummary) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      return response.ok({ data: listingRatingSummary })
    } catch (error) {
      Logger.error(error, 'findListingRatingSummary')
      return response.badRequest(error)
    }
  }

  public async findListingRatings({ params, request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const isExistListing = await Listing.find(params.id)

      if (!isExistListing) {
        return response.notFound({ success: false, message: 'Listing not found' })
      }

      const listingRatings = await ListingRating.filter(filters)
        .where('listing_id', params.id)
        .where('is_hidden', false)
        .preload('listing')
        .preload('session', (sessionQuery) => {
          sessionQuery.preload('user', (userQuery) => {
            userQuery.select('username')
          })
        })
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(listingRatings)
    } catch (error) {
      Logger.error(error, 'findListingRatings')
      return response.badRequest(error)
    }
  }

  public async findListingRating({ params: { id }, response }: HttpContextContract) {
    try {
      const listingRating = await ListingRating.query()
        .where('id', id)
        .andWhere('is_hidden', false)
        .preload('listing')
        .first()

      if (!listingRating) {
        return response.notFound({ success: false, message: 'Listing rating not found' })
      }

      return response.ok({ data: listingRating })
    } catch (error) {
      Logger.error(error, 'findListingRating')
      return response.badRequest(error)
    }
  }

  // Admin routes
  // R
  public async findListingRatingsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const listingRatings = await ListingRating.filter(filters)
        .preload('listing')
        .preload('session', (sessionQuery) => {
          sessionQuery.preload('user')
        })
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(listingRatings)
    } catch (error) {
      Logger.error(error, 'findListingRatingsAdmin')
      return response.badRequest(error)
    }
  }

  public async findListingRatingAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const listingRating = await ListingRating.query().where('id', id).preload('listing').first()

      if (!listingRating) {
        return response.notFound({ success: false, message: 'Listing rating not found' })
      }

      return response.ok({ data: listingRating })
    } catch (error) {
      Logger.error(error, 'findListingRatingAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateListingRating({ params: { id }, request, response }: HttpContextContract) {
    try {
      // Session & Listing ID cannot be updated
      const validationSchema = schema.create({
        app_rating: schema.number.optional([rules.range(0, 5)]),
        experience_rating: schema.number.optional([rules.range(0, 5)]),
        listing_rating: schema.number.optional([rules.range(0, 5)]),
        review: schema.string.optional(),
        is_hidden: schema.boolean.optional(),
      })

      const findListingRating = await ListingRating.find(id)

      if (!findListingRating) {
        return response.notFound({ success: false, message: 'Listing rating not found' })
      }

      let { review, ...validatedData } = await request.validate({ schema: validationSchema })

      const result = await Database.transaction(async (trx) => {
        review = review && statementFilter(review)
        findListingRating.merge({ ...validatedData, review: review })
        findListingRating.useTransaction(trx)
        await findListingRating.save()

        return findListingRating
      })

      Event.emit('sync:meilisearch', { type: 'update', listingId: findListingRating.listingId }) //Sync Meili with latest update

      return response.ok({
        success: true,
        message: 'Updated listing rating successfully',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateListingRating')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteListingRating({ params: { id }, response }: HttpContextContract) {
    try {
      const findListingRating = await ListingRating.find(id)

      if (!findListingRating) {
        return response.notFound({ success: false, message: 'Listing rating not found' })
      }

      const result = await Database.transaction(async (trx) => {
        findListingRating.useTransaction(trx)
        await findListingRating.delete()

        return {
          success: true,
        }
      })

      Event.emit('sync:meilisearch', { type: 'update', listingId: findListingRating.listingId }) //Sync Meili with latest update

      return response.ok({
        success: result.success,
        message: 'Deleted listing rating successfully',
      })
    } catch (error) {
      Logger.error(error, 'deleteListingRating')
      return response.badRequest(error)
    }
  }
}
