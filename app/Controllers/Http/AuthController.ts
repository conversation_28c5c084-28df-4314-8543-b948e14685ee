import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database, { TransactionClientContract } from '@ioc:Adonis/Lucid/Database'
import User, { userFindFields } from 'App/Models/User'
import Logger from '@ioc:Adonis/Core/Logger'
import Hash from '@ioc:Adonis/Core/Hash'
import { UserType } from 'Contracts/user_type'
import OneTimePassword from 'App/Models/OneTimePassword'
import { DateTime } from 'luxon'
import { OtpStatus } from 'Contracts/otp_type'
import { validatePhoneNumber } from 'App/utils'
import ShopifyService from 'App/Services/ShopifyService'
import Profile, { SocialType } from 'App/Models/Profile'
import { AuthContract } from '@ioc:Adonis/Addons/Auth'

const unexpected_err_msg = 'Unexpected internal server error'

export async function loginProcess(auth: AuthContract, user: User) {
  const token = await auth.use('api').generate(user)
  await token.user.load('favoriteListings')

  await createShopifyCustomerIfNotExists(token.user)

  await token.user.load('profiles')

  return token
}

// Helper function to create Shopify customer if not exists
export async function createShopifyCustomerIfNotExists(
  user: User,
  password?: string,
  trx?: TransactionClientContract
) {
  // cannot create Shopify customer if email is null or email is not verified
  if (user.emailAddress == null) {
    console.log('cannot create Shopify customer if email is null')

    return
  }

  const existingProfile = await Profile.query()
    .where('user_id', user.id)
    .where('social_type', SocialType.SHOPIFY)
    .first()

  if (existingProfile) {
    console.log('Shopify profile already exists')

    return
  }

  // This condition is for User that had soft deleted their gomama account, but created another new user row with the same email again.
  // Since we don't want to lose the generated password for our shopify profile for future login, when user soft deleted we set the shopify Profile foreign key 'user id' to null
  // Hence when login with same email again (which will create another new user row, since old row is soft deleted where email value is modified to xxx-deleted...), we just linked the new user id row that has same email with the existing shopify profile row
  const existingEmailShopifyProfile = await Profile.query()
    .where('email', user.emailAddress)
    .andWhereNull('user_id')
    .andWhere('social_type', SocialType.SHOPIFY)
    .first()

  if (existingEmailShopifyProfile) {
    existingEmailShopifyProfile.userId = user.id

    trx
      ? await existingEmailShopifyProfile.useTransaction(trx).save()
      : await existingEmailShopifyProfile.save()
  } else {
    try {
      const shopifyService = ShopifyService.getInstance()
      const generatedPassword = password || Math.random().toString(36).slice(-8)

      const shopifyCustomer = await shopifyService.createCustomer({
        email: user.emailAddress,
        phone: user.mobileNumber ? `+${user.countryDialCode}${user.mobileNumber}` : undefined,
        firstName: user.firstName,
        lastName: user.lastName,
        password: generatedPassword,
      })

      // Create Shopify profile for the user
      if (shopifyCustomer?.id) {
        await Profile.create(
          {
            userId: user.id,
            socialType: SocialType.SHOPIFY,
            socialId: shopifyCustomer.id,
            email: shopifyCustomer.email || user.emailAddress,
            firstName: user.firstName,
            lastName: user.lastName,
            encryptedPassword: generatedPassword, // This will be automatically encrypted by the beforeSave hook
          },
          trx ? { client: trx } : undefined
        )
      }
    } catch (error) {
      console.log('failed to create shopify customer', error)
    }
  }
}

export default class AuthController {
  public async login({ auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      login_account: schema.string({ trim: true }, [rules.minLength(1), rules.maxLength(255)]),
      password: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isEmail = emailPattern.test(validationData.login_account)
    const isPhone = validatePhoneNumber(validationData.login_account)

    if (!isEmail && !isPhone)
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })

    if (isPhone) {
      const fullMobileNumber = validationData.login_account

      const user = await User.query()
        .orderBy('created_at', 'desc')
        .select('*')
        .apply((scopes) => scopes.queryComparefullPhoneNumber(fullMobileNumber))
        .andWhere('is_hidden', false)
        .andWhereNull('deleted_at')
        .preload('favoriteListings')
        .first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not exist' })
      } else {
        try {
          if (!(await Hash.verify(user.password, validationData.password)))
            return response.unauthorized({ success: false, message: 'Password not match' })

          const token = await loginProcess(auth, user)

          return response.status(200).json({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } catch (error) {
          return response.status(400).send({ code: error.code, error: unexpected_err_msg })
        }
      }
    }

    if (isEmail) {
      const emailAddress = validationData.login_account

      const user = await User.query()
        .orderBy('created_at', 'desc')
        .select('*')
        .where('email_address', emailAddress)
        .andWhere('is_hidden', false)
        .andWhereNull('deleted_at')
        .first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not exist' })
      } else {
        try {
          if (!(await Hash.verify(user.password, validationData.password)))
            return response.unauthorized({ success: false, message: 'Password not match' })

          const token = await loginProcess(auth, user)

          return response.status(200).json({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } catch (error) {
          return response.badRequest({ code: error.code, error: unexpected_err_msg })
        }
      }
    }
  }

  public async loginWithOTP({ auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      login_account: schema.string({ trim: true }, [rules.minLength(1), rules.maxLength(255)]),
      otp: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const isEmail = emailPattern.test(validationData.login_account)
    const isPhone = validatePhoneNumber(validationData.login_account)

    if (!isEmail && !isPhone)
      return response.unprocessableEntity({
        success: false,
        message: 'Please provide either phone or email',
      })

    // Special case for App Store review demo account
    const isAppStoreReviewAccount =
      isEmail && validationData.login_account === '<EMAIL>'
    const isAppStoreReviewOtp = validationData.otp === '111111'

    // For normal accounts, validate OTP as usual
    let userLatestLoginOtp: OneTimePassword | null = null
    if (!isAppStoreReviewAccount || !isAppStoreReviewOtp) {
      userLatestLoginOtp = await OneTimePassword.query()
        .where('type', 'login')
        .andWhere('status', 'pending')
        .andWhere('expires_at', '>=', DateTime.now().toSQL())
        .orderBy('created_at', 'desc')
        .first()

      if (!userLatestLoginOtp)
        return response.unauthorized({
          success: false,
          message: 'OTP expired or not requested, please request a new OTP for login',
        })

      if (userLatestLoginOtp.expiresAt < DateTime.now()) {
        return response.badRequest({ success: false, message: 'The OTP has been expired' })
      }

      if (userLatestLoginOtp.oneTimePassword != validationData.otp) {
        return response.unauthorized({ success: false, message: 'The OTP does not match' })
      }
    }

    if (isPhone) {
      const fullMobileNumber = validationData.login_account

      const user = await User.query()
        .orderBy('created_at', 'desc')
        .select('*')
        .apply((scopes) => scopes.queryComparefullPhoneNumber(fullMobileNumber))
        .andWhere('is_hidden', false)
        .andWhereNull('deleted_at')
        .first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not exist' })
      } else {
        try {
          const token = await loginProcess(auth, user)

          if (userLatestLoginOtp) {
            userLatestLoginOtp.status = OtpStatus.verified
            await userLatestLoginOtp.save()
          }

          return response.status(200).json({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } catch (error) {
          return response.status(400).send({ code: error.code, error: unexpected_err_msg })
        }
      }
    }

    if (isEmail) {
      const emailAddress = validationData.login_account

      const user = await User.query()
        .orderBy('created_at', 'desc')
        .select('*')
        .where('email_address', emailAddress)
        .andWhere('is_hidden', false)
        .andWhereNull('deleted_at')
        .first()

      if (!user) {
        return response.notFound({ success: false, message: 'User not exist' })
      } else {
        try {
          const token = await loginProcess(auth, user)

          if (userLatestLoginOtp) {
            userLatestLoginOtp.status = OtpStatus.verified
            await userLatestLoginOtp.save()
          }

          return response.status(200).json({
            success: true,
            data: { user: token.user.serialize(userFindFields), token: token },
          })
        } catch (error) {
          return response.badRequest({ code: error.code, error: unexpected_err_msg })
        }
      }
    }
  }

  public async logout({ auth, response }: HttpContextContract) {
    const user = await auth.authenticate()
    try {
      // Remove eixsting token send via Authorization header only
      await auth.use('api').revoke()

      // Remove all existing token for a user
      await Database.rawQuery(`DELETE FROM api_tokens WHERE user_id = '${user.id}'`)

      return response.ok({ success: true, message: 'Successfully logout' })
    } catch (err) {
      return response.badRequest(err)
    }
  }

  public async logoutAdmin({ auth, response }: HttpContextContract) {
    try {
      // Remove existing token send via Authorization header only
      await auth.use('web').logout()

      return response.ok({ success: true, message: 'Successfully logout' })
    } catch (err) {
      return response.badRequest(err)
    }
  }

  public async adminLogin({ auth, response, request }: HttpContextContract) {
    const validationSchema = schema.create({
      email: schema.string({ trim: true }, []),
      password: schema.string({ trim: true }, []),
    })

    const validationData = await request.validate({
      schema: validationSchema,
    })

    try {
      // Check user is admin or not
      const loginUser = await User.findBy('email_address', validationData.email)

      if (!loginUser) return response.notFound({ success: false, message: 'User not exist' })

      if (loginUser.isHidden || loginUser.deletedAt)
        return response.notFound({ success: false, message: 'User not exist' })

      if (loginUser.userType != UserType.admin)
        return response.forbidden({
          success: false,
          message: 'Only admin user can login admin panel',
        })

      // login via cookie session
      const user = await auth.use('web').attempt(validationData.email, validationData.password)
      const tokenUser = await auth.use('api').generate(user)

      return response.status(200).json({ success: true, data: { user, token: tokenUser } })
    } catch (error) {
      Logger.error(error, 'adminLogin')
      return response.status(400).send(error)
    }
  }

  public async realtimeVerify({ auth, response, request }: HttpContextContract) {
    const userId = request.input('user_id')
    const user = await auth.authenticate()

    if (user.id == userId) {
      return response.status(200).json({
        success: true,
      })
    }

    return response.status(200).json({
      success: false,
    })
  }
}
