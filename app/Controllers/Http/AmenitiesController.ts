import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Amenity from 'App/Models/Amenity'
import _ from 'lodash'
import Logger from '@ioc:Adonis/Core/Logger'

export default class AmenitiesController {
  // R
  public async findAmenities({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const activities = await Amenity.query()
        .where('is_hidden', false)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(activities)
    } catch (error) {
      Logger.error(error, 'findAmenities')
      return response.badRequest(error)
    }
  }

  public async findAmenity({ params: { id }, response }: HttpContextContract) {
    try {
      const amenity = await Amenity.query().where('id', id).andWhere('is_hidden', false).first()

      if (!amenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      return response.ok({ data: amenity })
    } catch (error) {
      Logger.error(error, 'findAmenity')
      return response.badRequest(error)
    }
  }

  public async findAmenityWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const amenity = await Amenity.query().where('slug', slug).andWhere('is_hidden', false).first()

      if (!amenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      return response.ok({ data: amenity })
    } catch (error) {
      Logger.error(error, 'findAmenity')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createAmenity({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string([rules.minLength(1), rules.maxLength(255)]),
        description: schema.string([rules.minLength(1), rules.maxLength(255)]),
        font_icon_name: schema.string([rules.minLength(1), rules.maxLength(255)]),
      })

      const { ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const amenity = await Database.transaction(async (trx) => {
        const newAmenity = await Amenity.create({ ...validatedData }, { client: trx })

        await newAmenity.useTransaction(trx).save()

        return newAmenity
      })

      return response.ok({
        success: amenity.id ? true : false,
        message: 'Successfully created an amenity',
        data: amenity,
      })
    } catch (error) {
      Logger.error(error, 'createAmenity')
      return response.badRequest(error)
    }
  }

  // R
  public async findAmenitiesAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const activities = await Amenity.filter(filters).orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(activities)
    } catch (error) {
      Logger.error(error, 'findAmenitiesAdmin')
      return response.badRequest(error)
    }
  }

  public async findAmenityAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const amenity = await Amenity.find(id)

      if (!amenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      return response.ok({ data: amenity })
    } catch (error) {
      Logger.error(error, 'findAmenityAdmin')
      return response.badRequest(error)
    }
  }

  public async findAmenityWithSlugAdmin({ params: { slug }, response }: HttpContextContract) {
    try {
      const amenity = await Amenity.findBy('slug', slug)

      if (!amenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      return response.ok({ data: amenity })
    } catch (error) {
      Logger.error(error, 'findAmenityWithSlugAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateAmenity({ params: { id }, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        font_icon_name: schema.string.optional([rules.minLength(1), rules.maxLength(255)]),
        is_hidden: schema.boolean.optional(),
      })

      const findAmenity = await Amenity.find(id)

      if (!findAmenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      const { ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const result = await Database.transaction(async (trx) => {
        findAmenity.merge({ ...validatedData })

        await findAmenity.useTransaction(trx).save()

        return findAmenity
      })

      return response.ok({
        success: true,
        message: 'Successfully updated an amenity',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateAmenity')
      return response.badRequest(error)
    }
  }

  public async updateAmenityWithSlug({ params: { slug }, request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        name: schema.string.optional([rules.maxLength(255)]),
        description: schema.string.optional([rules.maxLength(255)]),
        font_icon_name: schema.string.optional([rules.minLength(1), rules.maxLength(255)]),
        is_hidden: schema.boolean.optional(),
      })

      const findAmenity = await Amenity.findBy('slug', slug)

      if (!findAmenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      const { ...validatedData } = await request.validate({
        schema: validationSchema,
      })

      const result = await Database.transaction(async (trx) => {
        findAmenity.merge({ ...validatedData })

        await findAmenity.useTransaction(trx).save()

        return findAmenity
      })

      return response.ok({
        success: true,
        message: 'Successfully updated an amenity',
        data: result,
      })
    } catch (error) {
      Logger.error(error, 'updateAmenityWithSlug')
      return response.badRequest(error)
    }
  }

  // D
  public async deleteAmenity({ params: { id }, response }: HttpContextContract) {
    try {
      const findAmenity = await Amenity.find(id)

      if (!findAmenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      const result = await Database.transaction(async (trx) => {
        findAmenity.useTransaction(trx)
        await findAmenity.delete()

        return {
          success: true,
        }
      })

      return response.ok({
        success: result.success,
        message: 'Successfully deleted an amenity',
      })
    } catch (error) {
      Logger.error(error, 'deleteAmenity')
      return response.badRequest(error)
    }
  }

  public async deleteAmenityWithSlug({ params: { slug }, response }: HttpContextContract) {
    try {
      const findAmenity = await Amenity.findBy('slug', slug)

      if (!findAmenity) {
        return response.notFound({ success: false, message: 'Amenity not found' })
      }

      const result = await Database.transaction(async (trx) => {
        findAmenity.useTransaction(trx)
        await findAmenity.delete()

        return {
          success: true,
        }
      })

      return response.ok({
        success: result.success,
        message: 'Successfully deleted an amenity',
      })
    } catch (error) {
      Logger.error(error, 'deleteAmenity')
      return response.badRequest(error)
    }
  }
}
