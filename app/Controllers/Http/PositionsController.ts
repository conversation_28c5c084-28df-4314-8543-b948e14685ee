import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { rules, schema } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Position from 'App/Models/Position'
import _ from 'lodash'
import * as ngeohash from 'ngeohash'
import { v4 as uuidv4 } from 'uuid'
import Logger from '@ioc:Adonis/Core/Logger'

export default class PositionsController {
  // R
  public async findPositions({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      // const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const positions = await Position.query()
        .where('is_hidden', false)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(positions)
    } catch (error) {
      Logger.error(error, 'findPositions')
      return response.badRequest(error)
    }
  }

  public async findPosition({ params: { id }, response }: HttpContextContract) {
    try {
      const position = await Position.query().where('is_hidden', false).andWhere('id', id).first()
      if (!position) {
        return response.notFound({ success: false, message: 'Position not found' })
      }

      return response.ok({ data: position })
    } catch (error) {
      Logger.error(error, 'findPosition')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createPosition({ request, response }: HttpContextContract) {
    try {
      const validationSchema = schema.create({
        longitude: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        latitude: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
      })

      const { longitude, latitude } = await request.validate({ schema: validationSchema })

      const trx = await Database.transaction()

      const existingPosition = await Position.query()
        .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
        .first()

      const geohash = ngeohash.encode(longitude, latitude, 20)

      if (existingPosition) {
        return response.conflict({ success: false, message: 'Position already exist.' })
      } else {
        // Position not exist create new position
        const newPositionId = uuidv4()
        const result = await Database.rawQuery(
          'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
          [newPositionId, geohash, longitude, latitude]
        ).useTransaction(trx)

        if (result[0].affectedRows > 0) {
          await trx.commit()
          const newPosition = await Position.find(newPositionId)
          if (!newPosition) {
            return response.badRequest({
              success: false,
              message: 'Failed to created position, please try again.',
            })
          }

          return response.ok({
            success: true,
            message: 'Successfully created new position',
          })
        } else {
          await trx.rollback()
          return response.badRequest({
            success: false,
            message: 'Failed to created position, please try again.',
          })
        }
      }
    } catch (error) {
      Logger.error(error, 'createPosition')
      return response.badRequest(error)
    }
  }

  // R
  public async findPositionsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      // const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const positions = await Position.query().orderBy(sort[0], sort[1]).paginate(page, limit)

      return response.ok(positions)
    } catch (error) {
      Logger.error(error, 'findPositionsAdmin')
      return response.badRequest(error)
    }
  }

  public async findPositionAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const position = await Position.find(id)
      if (!position) {
        return response.notFound({ success: false, message: 'Position not found' })
      }

      return response.ok({ data: position })
    } catch (error) {
      Logger.error(error, 'findPositionAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updatePosition({ params: { id }, request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      longitude: schema.number.optional([
        rules.requiredIfExists('latitude'),
        rules.range(-180, 180),
      ]),
      latitude: schema.number.optional([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
      is_hidden: schema.boolean.optional(),
    })
    const { longitude, latitude, is_hidden } = await request.validate({ schema: validationSchema })
    const trx = await Database.transaction()

    try {
      const findPosition = await Position.find(id)
      if (!findPosition) {
        return response.notFound({ success: false, message: 'Position not found' })
      }

      if (longitude && latitude) {
        const existingPosition = await Position.query()
          .whereRaw(`ST_Equals(coordinate, POINT(?, ?))`, [longitude, latitude])
          .first()
        if (existingPosition && existingPosition.id != id) {
          return response.conflict({ success: false, message: 'Position already exist.' })
        } else {
          // Position not exist create new position
          const geohash = ngeohash.encode(longitude, latitude, 20)
          const result = await Database.rawQuery(
            'UPDATE positions SET coordinate = POINT(?, ?), geo_hash = ? WHERE id = ?',
            [longitude, latitude, geohash, id]
          ).useTransaction(trx)

          if (result[0].affectedRows > 0) {
            if (is_hidden) {
              findPosition.isHidden = is_hidden
              await findPosition.useTransaction(trx).save()
            }
            await trx.commit()
            const updatedPosition = await Position.findOrFail(id)
            return response.ok({
              success: true,
              message: 'Successfully updated the position',
              data: updatedPosition,
            })
          } else {
            await trx.rollback()
            return response.badRequest({
              success: false,
              message: 'Failed to update the position, please try again.',
            })
          }
        }
      }
    } catch (error) {
      await trx.rollback()
      Logger.error(error, 'updatePosition')
      return response.badRequest(error)
    }
  }

  // D
  public async deletePosition({ params: { id }, response }: HttpContextContract) {
    try {
      const findPosition = await Position.find(id)

      if (!findPosition) {
        return response.notFound({ success: false, message: 'Position not found' })
      }

      const result = await Database.transaction(async (trx) => {
        findPosition.useTransaction(trx)
        await findPosition.delete()

        return {
          success: true,
        }
      })

      return response.ok({
        success: result.success,
        message: 'Successfully deleted the position',
      })
    } catch (error) {
      Logger.error(error, 'deletePosition')
      return response.badRequest(error)
    }
  }
}
