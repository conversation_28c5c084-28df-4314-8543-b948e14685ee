import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import Session from 'App/Models/Session'
import _ from 'lodash'
import axios from 'axios'
import Listing from 'App/Models/Listing'
import { DateTime } from 'luxon'
import Logger from '@ioc:Adonis/Core/Logger'
import { ListingStatus, ListingType } from 'Contracts/listing_type'
import SessionService from 'App/Services/SessionService'
import { UserGender } from 'Contracts/users'
import { getDistanceFromLatLonInKm, varcharRule } from 'App/utils'
import Env from '@ioc:Adonis/Core/Env'
import Redis from '@ioc:Adonis/Addons/Redis'
import { REDIS_KEYS } from 'App/Services/RedisService'

interface PinBody {
  variance: number | undefined
  startDate: string
  endDate: string | undefined
  permissions: string[] | undefined
}

async function igloohomePinRequest(
  pinBody: PinBody,
  endpoint: string,
  type: 'bluetooth_guestkey' | 'one_time' | 'hour' | 'day'
) {
  let variance = Math.floor(Math.random() * 5)
  if (variance === 0) variance = 1
  const igloohomeApiKey = Env.get('IGLOOHOME_API_KEY')

  try {
    const pin = await axios.post(endpoint, pinBody, {
      headers: {
        'X-Igloocompany-Apikey': igloohomeApiKey,
        'Content-Type': 'application/json',
      },
    })

    return type == 'bluetooth_guestkey'
      ? pin?.data['bluetoothGuestKey'] ?? ''
      : pin?.data['pin'] ?? ''
  } catch (e) {
    console.log(`Call Igloohome ${type} pin endpoint to generate ${type} pin.`)
  }
}

async function iGlooHomePreparePinReq(
  listing: Listing,
  started_at: DateTime,
  requestAll: boolean = true,
  pinType: 'bluetooth_guestkey' | 'one_time' | 'hour' | 'day' | undefined = undefined
): Promise<string[]> {
  const guestPermissions = [
    'UNLOCK',
    'SET_TIME',
    'GET_TIME',
    'GET_BATTERY_LEVEL',
    'GET_LOCK_STATUS',
    'SET_AUTORELOCK',
    'CREATE_PIN',
    'LOCK',
  ]
  const startDate = started_at
  const igloohomeUrl = 'https://api.igloodeveloper.co/v2'

  // BT Guest Key Endpoint & Body
  const igloohomeGuestKeyEndpoint = `${igloohomeUrl}/locks/${listing.lockId}/ekeys`
  const guestKeyBody: PinBody = {
    variance: undefined,
    startDate: startDate.toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    endDate: startDate.plus({ days: 1 }).toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    permissions: guestPermissions,
  }

  // One Time Pin Endpoint & Body
  const igloohomeOneTimePinEndpoint = `${igloohomeUrl}/locks/${listing.lockId}/pin/onetime`
  let variance = Math.floor(Math.random() * 5)
  const oneTimePinBody: PinBody = {
    variance: variance === 0 ? 1 : variance,
    startDate: startDate.toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    endDate: undefined,
    permissions: undefined,
  }

  // Hourly Pin Endpoint & Body
  const igloohomeHourlyPinEndpoint = `${igloohomeUrl}/locks/${listing.lockId}/pin/hourly`
  variance = Math.floor(Math.random() * 3)
  if (variance === 0) {
    variance = 1
  }
  const hourlyPinBody: PinBody = {
    variance,
    startDate: startDate.toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    endDate: startDate.plus({ hours: 1 }).toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    permissions: undefined,
  }

  // Daily Pin Endpoint & Body
  const igloohomeDailyPinEndpoint = `${igloohomeUrl}/locks/${listing.lockId}/pin/daily`
  variance = Math.floor(Math.random() * 3)
  if (variance === 0) {
    variance = 1
  }
  const dailyPinBody: PinBody = {
    variance,
    startDate: startDate.toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    endDate: startDate.plus({ days: 29 }).toFormat(`yyyy-MM-dd'T'HH:00:00ZZ`),
    permissions: undefined,
  }

  let bluetoothGuestKey: string = ''
  let oneTimePin: string = ''
  let hourlyPin: string = ''
  let dailyPin: string = ''

  // Get all requested pin
  if (pinType == 'bluetooth_guestkey' || requestAll) {
    bluetoothGuestKey = await igloohomePinRequest(
      guestKeyBody,
      igloohomeGuestKeyEndpoint,
      'bluetooth_guestkey'
    )
  }
  if (pinType == 'one_time' || requestAll) {
    oneTimePin = await igloohomePinRequest(oneTimePinBody, igloohomeOneTimePinEndpoint, 'one_time')
  }
  if (pinType == 'hour' || requestAll) {
    hourlyPin = await igloohomePinRequest(hourlyPinBody, igloohomeHourlyPinEndpoint, 'hour')
  }
  if (pinType == 'day' || requestAll) {
    dailyPin = await igloohomePinRequest(dailyPinBody, igloohomeDailyPinEndpoint, 'day')
  }

  return [bluetoothGuestKey, oneTimePin, hourlyPin, dailyPin]
}

export default class SessionsController {
  // C
  public async createSession({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'createSession')

    let successPinReq = false
    const user = await auth.authenticate()

    try {
      const validationSchema = schema.create({
        listing_id: schema.string([rules.exists({ column: 'id', table: 'listings' })]),
        lock_custom_pin: schema.string.optional(varcharRule),
        duration: schema.enum([30, 45]),
        lon: schema.number([rules.requiredIfExists('latitude'), rules.range(-180, 180)]),
        lat: schema.number([rules.requiredIfExists('longitude'), rules.range(-90, 90)]),
      })

      /// NOTE: clear the existing data before we proceed, in order to clear users old sessions
      await SessionService.emitUpdate(user.id, {
        type: 'no_active_session',
      })

      const validationData = await request.validate({ schema: validationSchema })

      const existingOngoingSessionOfUser = await Session.query()
        .whereNull('actual_ended_at')
        .andWhere('user_id', user.id)
        .first()

      if (existingOngoingSessionOfUser) {
        Logger.error('User currently has an on-going session')

        // return original session
        // const ongoingSession = await Session.query()
        //   .where('listing_id', validationData.listing_id)
        //   .andWhereNull('actual_ended_at')
        //   .first()

        // check if the session is expired
        // const now = DateTime.now()
        // if (ongoingSession?.expectedEndedAt && now > ongoingSession.expectedEndedAt) {
        //   // end the session for them right away
        //   ongoingSession.actualEndedAt = ongoingSession.expectedEndedAt
        //   await ongoingSession.save()

        //   await SessionService.emitUpdate(user.id, {
        //     type: 'session_ended',
        //     ...ongoingSession.toJSON(),
        //   })
        // } else {
        const existingOngoingSessionListing = await Listing.find(
          existingOngoingSessionOfUser.listingId
        )

        if (existingOngoingSessionListing?.status == ListingStatus.idle) {
          // possible scenario: they created a session but failed to receive any webhook
          // we want to allow them to kill session & restart the process
          SessionService.emitUpdate(user.id, {
            type: 'user_ongoing_session_with_restart',
          })
        } else {
          SessionService.emitUpdate(user.id, {
            type: 'user_ongoing_session',
          })
        }

        return response.forbidden({
          success: false,
          message: 'You still have an on-going session',
          code: 'ERR_SESSION_ONGOING',
        })
        // }
      }

      const listing = await Listing.query()
        .where('id', validationData.listing_id)
        .preload('position')
        .firstOrFail()

      const metresBetweenUserNListing =
        getDistanceFromLatLonInKm(
          listing.position.coordinate.y,
          listing.position.coordinate.x,
          validationData.lat,
          validationData.lon
        ) * 1000

      const allowedMetreDistance = Env.get('IGLOOHOME_DISTANCE')
      if (metresBetweenUserNListing > allowedMetreDistance) {
        SessionService.emitUpdate(user.id, {
          type: 'exceed_distance',
        })

        return response.forbidden({
          success: false,
          message: `The distance between the user and the listing must be within ${allowedMetreDistance} meters before the listing can be requested for unlocking.`,
          code: 'ERR_DISTANCE_TOO_FAR',
        })
      }

      if (listing.status != ListingStatus.idle) {
        Logger.error('Listing is occupied or undergoing disinfection')
        return response.forbidden({
          success: false,
          message:
            listing.status == ListingStatus.disinfecting
              ? 'The listing is currently undergoing disinfection.'
              : 'The listing is currently occupied.',
        })
      }

      if (listing.listingType != ListingType.gomama) {
        Logger.error('Only GO!Mama pods can be accessed')
        return response.forbidden({
          success: false,
          message: 'Only GO!Mama pods can be accessed',
          code: 'ERR_ACCESS_RESTRICTED',
        })
      }

      if (!listing.lockId) {
        Logger.error('Listing without lock cannot create session')
        return response.forbidden({
          success: false,
          message: "You don't have to unlock this listing",
          code: 'ERR_SESSION_NO_LOCK',
        })
      }

      const onGoingExist = await Session.query()
        .where('listing_id', validationData.listing_id)
        .andWhereNull('actual_ended_at')
        .first()

      if (onGoingExist) {
        Logger.error(
          'There is an existing on-going session for the listing, a new session can only be created after its last session has ended'
        )

        // check if the session is expired
        // const now = DateTime.now()
        // if (onGoingExist?.expectedEndedAt && now > onGoingExist.expectedEndedAt) {
        //   // end the session for them right away
        //   onGoingExist.actualEndedAt = onGoingExist.expectedEndedAt
        //   await onGoingExist.save()

        //   await SessionService.emitUpdate(user.id, {
        //     type: 'session_ended',
        //     ...onGoingExist.toJSON(),
        //   })
        // } else {
        SessionService.emitUpdate(user.id, {
          type: 'listing_ongoing_session',
        })

        return response.forbidden({
          success: false,
          message:
            'There is an existing on-going session for the listing, a new session can only be created after its last session has ended',
          code: 'ERR_SESSION_EXISTING',
        })
        // }
      }

      if (user.childrenBirthdays && user.childrenBirthdays?.length > 0) {
        const youngestChildBirthdate = DateTime.max(
          ...user.childrenBirthdays.map((birthday) =>
            DateTime.fromFormat(birthday + '', 'yyyy-MM-dd')
          )
        )

        if (youngestChildBirthdate.plus({ years: 3 }) < DateTime.now()) {
          Logger.info(`Your youngest child is above 3 years old`)
          SessionService.emitUpdate(user.id, {
            type: 'child_exceed_age',
          })

          return response.forbidden({
            success: false,
            message: `Your youngest child is above 3 years old`,
          })
        }
      } else {
        SessionService.emitUpdate(user.id, {
          type: 'no_child',
        })
        Logger.info(`You don't have any children`)

        return response.forbidden({ success: false, message: `You don't have any children` })
      }

      if (user.gender != UserGender.female) {
        Logger.info('Only female are allowed')
        return response.forbidden({ success: false, message: 'Only female are allowed' })
      }

      const started_at = DateTime.now()
      const expected_ended_at = started_at.plus({
        minutes: validationData.duration ?? listing.usageDurations[0] ?? 30, // first expected usage duration is default to 30 minutes if not specified in listing usage_duration column
      })

      const pinArr = await iGlooHomePreparePinReq(listing, started_at)

      if (pinArr.every((pin) => pin !== '')) {
        successPinReq = true
      }

      if (!successPinReq) {
        Logger.error('Some pins request failed')
        return response.badRequest({
          success: false,
          message: 'Some pins request failed',
          code: 'ERR_PINS_REQUEST_FAILED',
        })
      }

      const session = await Database.transaction(async (trx) => {
        const newSession = await Session.create(
          {
            listingId: validationData.listing_id,
            firestoreId: listing.firestoreId,
            userId: user.id,
            lockBluetoothGuestKey: pinArr[0],
            lockCustomPin: validationData.lock_custom_pin,
            lockDailyPin: pinArr[3],
            lockHourlyPin: pinArr[2],
            lockOneTimePin: pinArr[1],
            startedAt: started_at,
            expectedEndedAt: expected_ended_at,
          },
          { client: trx }
        )
        return newSession
      })

      Logger.info('Session created')

      await SessionService.emitUpdate(user.id, {
        type: 'session_created',
        ...session.toJSON(),
      })

      await session.load('listing')
      SessionService.emitUpdate(user.id, {
        type: 'active_session',
        ...session.toJSON(),
      })

      // find all the devices of this user
      await user.load('devices')

      // Store device IDs as a list in Redis
      try {
        // Added try-catch to handle the scenario when gomama_flutter fails to get FCM (device token)
        // hence failed to create device
        // empty array will trigger Redis error: ERR wrong number of arguments for 'sadd' command
        // We ignore this error as it is not critical since the purpose is only for sending feedback form reminder
        // We expect gomama_flutter will try to recreate the device when restart the app.
        await Redis.sadd(
          `${REDIS_KEYS.DEVICE_IDS}:${user.id}`,
          ...user.devices.map((device) => device.deviceToken)
        )
      } catch (err) {
        Logger.error('createSession.Redis.sadd(device_ids:user_id)', err)
      }

      return response.ok({
        success: session.id && successPinReq ? true : false,
        message: 'Session created successfully',
        data: session,
      })
    } catch (error) {
      Logger.error(error, 'createSession')
      return response.badRequest(error)
    }
  }

  public async regeneratePin({ request, response, params, auth }: HttpContextContract) {
    Logger.info(request.all(), 'regeneratePin')

    let successPinReq = false
    const user = await auth.authenticate()

    try {
      const findSession = await Session.query()
        .where('id', params.id)
        .where('user_id', user.id)
        .whereNull('actual_ended_at')
        .preload('listing')
        .first()

      if (!findSession) {
        Logger.error('Session not found')
        return response.notFound({
          success: false,
          message: 'Session not found',
          code: 'ERR_SESSION_NOT_FOUND',
        })
      }

      const pinArr = await iGlooHomePreparePinReq(findSession.listing, DateTime.now())

      if (pinArr.every((pin) => pin !== '')) {
        successPinReq = true
      }

      if (!successPinReq) {
        Logger.error('Some pins request failed')
        return response.badRequest({
          success: false,
          message: 'Some pins request failed',
          code: 'ERR_PINS_REQUEST_FAILED',
        })
      }

      const session = await Database.transaction(async (trx) => {
        if (findSession.lockOneTimePin == pinArr[1] || findSession.lockOneTimePin == pinArr[2]) {
          if (findSession.lockOneTimePin == pinArr[1]) {
            //  When Lock One Time Pin does not get updated from the Igloohome API, use hourly pin to update instead.
            findSession.lockOneTimePin = pinArr[2]
          } else if (findSession.lockOneTimePin == pinArr[2]) {
            //  When Lock One Time Pin & Lock Hourly Pin does not get updated from the Igloohome API, use daily pin to update instead.
            findSession.lockOneTimePin = pinArr[3]
          }

          findSession.lockBluetoothGuestKey = pinArr[0]
          findSession.lockDailyPin = pinArr[3]
          findSession.lockHourlyPin = pinArr[2]
        } else {
          findSession.lockBluetoothGuestKey = pinArr[0]
          findSession.lockDailyPin = pinArr[3]
          findSession.lockHourlyPin = pinArr[2]
          findSession.lockOneTimePin = pinArr[1]
        }

        await findSession.useTransaction(trx).save()

        return findSession
      })

      SessionService.emitUpdate(user.id, {
        type: 'active_session',
        ...session.toJSON(),
      })

      return response.ok({
        success: session.id && successPinReq ? true : false,
        message: 'PIN regenerated successfully',
        data: session,
      })
    } catch (error) {
      Logger.error(error, 'regeneratePin')
      return response.badRequest(error)
    }
  }

  // R
  public async findSessions({ request, response, auth }: HttpContextContract) {
    Logger.info(request.all(), 'findSessions')
    const user = await auth.authenticate()

    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const sessions = await Session.filter(filters)
        .where('sessions.is_hidden', false)
        .andWhere('user_id', user.id)
        .andWhereNotNull('actual_ended_at')
        .if(sort[0] == 'name', (query) =>
          query.innerJoin('listings', 'sessions.listing_id', '=', 'listings.id')
        )
        .preload('listing', (query) =>
          query
            .select('name', 'position_id', 'region_id', 'firestore_id')
            .preload('listingFiles', (query) => {
              query.where('listing_files.is_hidden', false).where('is_main', true)
            })
            .withAggregate('listingRatings', (query) => {
              query.preload('session').avg('experience_rating').as('average_experience_ratings')
            })
        )
        .preload('listingRating')
        .if(sort[0] == 'created_at', (query) => query.orderBy('sessions.created_at', sort[1]))
        .if(sort[0] == 'name', (query) => query.orderBy('listings.name', sort[1]))
        .paginate(page, limit)

      return response.ok(sessions)
    } catch (error) {
      Logger.error(error, 'findSessions')
      return response.badRequest(error)
    }
  }

  public async findSession({ params: { id }, response, auth }: HttpContextContract) {
    const user = await auth.authenticate()

    try {
      const session = await Session.query()
        .where('id', id)
        .andWhere('user_id', user.id)
        .andWhere('is_hidden', false)
        .preload('listing', (query) => {
          query.preload('listingFiles', (query) => {
            query.where('is_hidden', false)
          })
        })
        .preload('listingRating')
        .first()

      if (!session) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      return response.ok({ data: session })
    } catch (error) {
      Logger.error(error, 'findSession')
      return response.badRequest(error)
    }
  }

  public async findActiveSession({ response, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    Logger.info(user.emailAddress ?? user.mobileNumber, 'findActiveSession')

    try {
      const session = await Session.query()
        .whereNull('actual_ended_at')
        .andWhere('user_id', user.id)
        .andWhere('is_hidden', false)
        .first()

      if (!session) {
        return response.ok({ success: false, message: 'No active session' })
      }

      return response.ok({ success: true, data: session })
    } catch (error) {
      Logger.error(error, 'findActiveSession')
      return response.badRequest(error)
    }
  }

  public async extendSession({ response, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    Logger.info(user.emailAddress ?? user.mobileNumber, 'extendSession')

    try {
      const activeSession = await Session.query()
        .whereNull('actual_ended_at')
        .andWhere('user_id', user.id)
        .andWhere('is_hidden', false)
        .preload('listing', (query) => {
          query.preload('listingFiles', (query) => {
            query.where('is_hidden', false)
          })
        })
        .first()

      if (!activeSession) {
        return response.notFound({ success: false, message: 'No active session found' })
      }

      if (!activeSession.listing.isUsageExtendable) {
        return response.forbidden({
          success: false,
          message: 'Usage duration is not extendable for the said listing.',
        })
      }

      if (activeSession.expectedEndedAt < DateTime.now()) {
        return response.badRequest({
          success: false,
          message: 'The session has already reached expected ended period, cannot be extended.',
        })
      }

      // double check if this session can extend
      if (
        activeSession.numberOfUsageExtensions >=
        (activeSession.listing.maxNumberOfUsageExtensions ?? 1)
      ) {
        return response.forbidden({
          success: false,
          message: 'Session has reached maximum number of extension for the said listing.',
          listing_maximum_extension: activeSession.listing.maxNumberOfUsageExtensions ?? 1,
        })
      }

      activeSession.numberOfUsageExtensions += 1
      activeSession.expectedEndedAt = DateTime.fromISO(activeSession.expectedEndedAt.toISO()!).plus(
        {
          minutes: activeSession.listing.usageExtensionDurations[0] ?? 15,
        }
      )

      // Update redis
      const redisSessionData = await Redis.hgetall(`${REDIS_KEYS.SESSION_HASH}:${user.id}`)
      const newExpectedEndedAt = activeSession.expectedEndedAt
      const newExpectedUsageDuration = activeSession.expectedEndedAt
      await Redis.hset(`${REDIS_KEYS.SESSION_HASH}:${user.id}`, {
        ...redisSessionData,
        expected_usage_duration: newExpectedUsageDuration,
        expected_ended_at: newExpectedEndedAt,
      })

      await activeSession.save()

      SessionService.emitUpdate(user.id, {
        type: 'session_extended',
        ...activeSession.toJSON(),
      })

      SessionService.emitUpdate(user.id, {
        type: 'active_session',
        ...activeSession.toJSON(),
      })

      return response.ok({ success: true, data: activeSession })
    } catch (error) {
      Logger.error(error, 'extendSession')
      return response.badRequest(error)
    }
  }

  public async endSession({ request, response, auth }: HttpContextContract) {
    const user = await auth.authenticate()
    Logger.info(user.emailAddress ?? user.mobileNumber, 'endSession')

    const isRestart = request.input('restart', false)

    try {
      const activeSession = await Session.query()
        .whereNull('actual_ended_at')
        .andWhere('user_id', user.id)
        .andWhere('is_hidden', false)
        .preload('listing')
        .first()

      if (!activeSession) {
        SessionService.emitUpdate(user.id, {
          type: 'no_active_session',
        })

        return response.notFound({ success: false, message: 'No active session found' })
      }

      // check if listing status is still occupied
      const listingStatus = await Redis.hget(
        REDIS_KEYS.ACTIVE_LISTINGS,
        activeSession.listing.firestoreId
      )

      if (listingStatus == ListingStatus.occupied) {
        // TODO: do we need to check if current 'occupied' belongs to this session?
        return response.badRequest({
          success: false,
          message: 'Please exit the pod before ending the session',
        })
      }

      // only wait for auto terminate if not restart (triggered by user in-app)
      if (!isRestart) {
        if (listingStatus == ListingStatus.idle) {
          // TODO: do we need to check if current 'idle' belongs to this session?
          return response.badRequest({
            success: false,
            message:
              'Please enter the pod first. \nOr wait the session to terminate automatically.',
          })
        }
      }

      activeSession.actualEndedAt = DateTime.now()

      const updatedSession = await activeSession.save()

      if (!isRestart) {
        SessionService.emitUpdate(user.id, {
          type: 'session_ended',
          ...activeSession.toJSON(),
        })

        SessionService.emitUpdate(user.id, {
          type: 'no_active_session',
        })
      }

      return response.ok({ success: true, data: updatedSession })
    } catch (error) {
      Logger.error(error, 'endSession')
      return response.badRequest(error)
    }
  }

  // Admin Routes

  // C
  public async createSessionAdmin({ request, response }: HttpContextContract) {
    let success = false

    try {
      const validationSchema = schema.create({
        user_id: schema.string([rules.exists({ column: 'id', table: 'users' })]),
        listing_id: schema.string([rules.exists({ column: 'id', table: 'listings' })]),
        lock_custom_pin: schema.string.optional(varcharRule),
        usage_duration: schema.number.optional(),
      })

      const validationData = await request.validate({ schema: validationSchema })

      const existUserOngoing = await Session.query()
        .whereNull('actual_ended_at')
        .andWhere('user_id', validationData.user_id)
        .andWhereNotNull('listing_id')
        .first()

      if (existUserOngoing) {
        Logger.error('User currently has an on-going session')
        return response.forbidden({
          success: false,
          message: 'User currently has an on-going session',
          code: 'ERR_SESSION_ONGOING',
        })
      }

      const listing = await Listing.findOrFail(validationData.listing_id)

      if (listing.status != ListingStatus.idle) {
        return response.forbidden({
          success: false,
          message:
            listing.status == ListingStatus.disinfecting
              ? 'The listing is currently undergoing disinfection.'
              : 'The listing is currently occupied.',
        })
      }

      if (!listing.lockId) {
        Logger.error('Listing without lock cannot create session')
        return response.forbidden({
          success: false,
          message: 'Listing without lock cannot create session',
          code: 'ERR_SESSION_NO_LOCK',
        })
      }

      const onGoingExist = await Session.query()
        .where('listing_id', validationData.listing_id)
        .whereNull('actual_ended_at')
        .first()

      if (onGoingExist) {
        Logger.error(
          'There is an existing on-going session for the listing, a new session can only be created after its last session has ended'
        )
        return response.forbidden({
          success: false,
          message:
            'There is an existing on-going session for the listing, a new session can only be created after its last session has ended',
          code: 'ERR_SESSION_EXISTING',
        })
      }

      const started_at = DateTime.now()
      const expected_ended_at = started_at.plus({
        minutes: validationData.usage_duration ?? listing.usageExtensionDurations[0] ?? 30,
      })

      const pinArr = await iGlooHomePreparePinReq(listing, started_at)

      if (pinArr.every((pin) => pin !== '')) {
        success = true
      }

      if (!success) {
        return response.badRequest({
          success: false,
          message: 'Igloohome API Pin request failed.',
        })
      }

      const session = await Database.transaction(async (trx) => {
        const newSession = await Session.create(
          {
            listingId: validationData.listing_id,
            userId: validationData.user_id,
            lockBluetoothGuestKey: pinArr[0],
            lockCustomPin: validationData.lock_custom_pin,
            lockDailyPin: pinArr[3],
            lockHourlyPin: pinArr[2],
            lockOneTimePin: pinArr[1],
            startedAt: started_at,
            expectedEndedAt: expected_ended_at,
            firestoreId: listing.firestoreId,
          },
          { client: trx }
        )
        return newSession
      })

      return response.ok({
        success: session.id && success ? true : false,
        message: 'Session created successfully',
        data: session,
      })
    } catch (error) {
      Logger.error(error, 'createSessionAdmin')
      return response.badRequest(error)
    }
  }

  // R
  public async findSessionsAdmin({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)
      const filters = _.omit(request.all(), ['page', 'sort', 'limit'])

      const sessions = await Session.filter(filters)
        .preload('user')
        .preload('listing')
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok(sessions)
    } catch (error) {
      Logger.error(error, 'findSessionsAdmin')
      return response.badRequest(error)
    }
  }

  public async findSessionAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const session = await Session.query()
        .where('id', id)
        .preload('listing', (query) => {
          query.preload('listingFiles')
        })
        .preload('listingRating')
        .preload('user')
        .first()

      if (!session) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      return response.ok({ data: session })
    } catch (error) {
      Logger.error(error, 'findSessionAdmin')
      return response.badRequest(error)
    }
  }

  public async findSessionsByUserIdAdmin({
    params: { user_id },
    request,
    response,
  }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const sort = request.input('sort', 'created_at:desc').split(':')
      const limit = request.input('limit', 20)

      const session = await Session.query()
        .where('user_id', user_id)
        .orderBy(sort[0], sort[1])
        .paginate(page, limit)

      return response.ok({ data: session })
    } catch (error) {
      Logger.error(error, 'findSessionsByUserIdAdmin')
      return response.badRequest(error)
    }
  }

  // U
  public async updateSessionLockPins({
    params: { id },
    request,
    response,
    auth,
  }: HttpContextContract) {
    try {
      const user = await auth.authenticate()
      const findSession = await Session.find(id)
      if (!findSession) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      if (findSession.actualEndedAt) {
        return response.forbidden({ success: false, message: 'Session has already been ended' })
      }

      const findListing = await Listing.findOrFail(findSession.listingId)

      const validationSchema = schema.create({
        lock_bluetooth_guestkey_update: schema.boolean.optional(),
        lock_daily_pin_update: schema.boolean.optional(),
        lock_hourly_pin_update: schema.boolean.optional(),
        lock_one_time_pin_update: schema.boolean.optional(),
        lock_custom_pin: schema.string.optional(varcharRule),
      })

      const {
        lock_bluetooth_guestkey_update,
        lock_daily_pin_update,
        lock_hourly_pin_update,
        lock_one_time_pin_update,
        lock_custom_pin,
      } = await request.validate({ schema: validationSchema })

      const dateNow = DateTime.now()

      const lockBluetoothGuestKey: string | undefined = lock_bluetooth_guestkey_update
        ? (await iGlooHomePreparePinReq(findListing, dateNow, false, 'bluetooth_guestkey'))[0]
        : undefined
      const lockDailyPin: string | undefined = lock_daily_pin_update
        ? (await iGlooHomePreparePinReq(findListing, dateNow, false, 'day'))[3]
        : undefined
      const lockHourlyPin: string | undefined = lock_hourly_pin_update
        ? (await iGlooHomePreparePinReq(findListing, dateNow, false, 'hour'))[2]
        : undefined
      const lockOneTimePin: string | undefined = lock_one_time_pin_update
        ? (await iGlooHomePreparePinReq(findListing, dateNow, false, 'one_time'))[1]
        : undefined

      const activeSession = await Database.transaction(async (trx) => {
        findSession.merge({
          lockCustomPin: lock_custom_pin,
          lockBluetoothGuestKey,
          lockDailyPin,
          lockHourlyPin,
          lockOneTimePin,
        })
        findSession.useTransaction(trx)
        await findSession.save()

        return findSession
      })

      SessionService.emitUpdate(user.id, {
        type: 'active_session',
        ...activeSession.toJSON(),
      })

      return response.ok({
        success: true,
        message: 'Updated session lock-pins successfully',
        data: activeSession,
      })
    } catch (error) {
      Logger.error(error, 'updateSessionLockPins')
      return response.badRequest(error)
    }
  }

  public async endSessionAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const findSession = await Session.find(id)
      if (!findSession) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      if (findSession.actualEndedAt) {
        return response.forbidden({ success: false, message: 'Session has already been ended' })
      }

      findSession.actualEndedAt = DateTime.now()
      await findSession.save()

      return response.ok({ success: true, message: 'Session has been ended successfully' })
    } catch (error) {
      Logger.error(error, 'endSession')
      return response.badRequest(error)
    }
  }

  public async hideNUnhideSessionAdmin({ params: { id }, response }: HttpContextContract) {
    try {
      const findSession = await Session.find(id)
      if (!findSession) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      if (!findSession.actualEndedAt)
        return response.forbidden({
          success: false,
          message: 'The session is not ended yet, cannot be hidden',
        })

      const wasHidden = findSession.isHidden

      findSession.isHidden = !findSession.isHidden

      await findSession.save()

      return response.ok({
        success: true,
        message: `Session has been ${wasHidden ? 'unhidden' : 'hidden'} successfully`,
      })
    } catch (error) {
      Logger.error(error, 'hideNUnhideSessionAdmin')
      return response.badRequest(error)
    }
  }
  // D
  public async deleteSession({ params: { id }, response }: HttpContextContract) {
    try {
      const findSession = await Session.find(id)

      if (!findSession) {
        return response.notFound({ success: false, message: 'Session not found' })
      }

      if (!findSession.actualEndedAt) {
        return response.forbidden({ success: false, message: 'Cannot delete an on-going session' })
      }

      const result = await Database.transaction(async (trx) => {
        findSession.useTransaction(trx)
        await findSession.delete()

        return {
          success: true,
        }
      })

      return response.ok({
        success: result.success,
        message: 'Deleted session successfully',
      })
    } catch (error) {
      Logger.error(error, 'deleteSession')
      return response.badRequest(error)
    }
  }
}
