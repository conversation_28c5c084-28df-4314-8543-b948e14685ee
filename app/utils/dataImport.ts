import Activity from 'App/Models/Activity'
import Amenity from 'App/Models/Amenity'
import Listing from 'App/Models/Listing'
// import ListingType from 'App/Models/ListingType'
import Region from 'App/Models/Region'
import Session from 'App/Models/Session'
import User from 'App/Models/User'
// import UserType from 'App/Models/UserType'
import { UserType } from 'Contracts/user_type'
import fs from 'fs'
import path from 'path'
import ListingRating from 'App/Models/ListingRating'
import { UserGender } from 'Contracts/users'
import Position from 'App/Models/Position'
import Database from '@ioc:Adonis/Lucid/Database'
import * as ngeohash from 'ngeohash'
import ListingFile from 'App/Models/ListingFile'
import { v4 as uuidv4 } from 'uuid'
import { ListingStatus } from 'Contracts/listing_type'
import { DateTime } from 'luxon'
import { generateRandomCode } from '.'
import UserVerificationRequest from 'App/Models/UserVerificationRequest'
import {
  UserVerificationStatus,
  UserVerificationType,
} from 'Contracts/user_verification_request_type'
import Drive from '@ioc:Adonis/Core/Drive'
import { isEmpty } from 'radash'

export enum dirName {
  activity = '1_activity',
  amenity = '2_amenity',
  // listingTypes = '3_listing_types',
  regions = '4_regions',
  // userTypes = '5_user_types',
  users = '6_users',
  listings = '7_listings',
  sessions = '8_sessions',
  listingRatings = '9_listing_ratings',
  userListings = '10_user_listings',
}

async function base64ToImage(
  base64String: string,
  imageFolder: string,
  fileName: string,
  isMain: boolean = false,
  isPrivate: boolean = false
): Promise<string> {
  const imgFileFullName = `${fileName}${isMain ? '_' + generateRandomCode(10) : ''}.png`
  const file = `${imageFolder}/${imgFileFullName}`
  const buffer = Buffer.from(base64String, 'base64')
  if (!base64String) return '' // No 64 base string (empty) return empty url

  let disk: 's3_public' | 's3_private' = 's3_public'
  if (isPrivate) {
    disk = 's3_private'
  }

  try {
    if (await Drive.use(disk).exists(file)) {
      return imgFileFullName
    }

    // uploading public photos here
    await Drive.use(disk).put(file, buffer, {
      visibility: 'private',
    })

    return imgFileFullName
  } catch (error) {
    console.log(error)
    return ''
  }
}

export async function importData(dirName: dirName): Promise<any[]> {
  //All new row promises array

  const directoryPath = './production'
  const nonExistActivitytValue: string[] = []
  const nonExistAmenitytValue: string[] = []
  const nonExistRegionValue: string[] = []
  const nonExistListing: string[] = []
  const nonExistUser: string[] = []

  let numRows: number = 0

  const dirRetrieved = fs.readdirSync(path.join(directoryPath, dirName))

  const jsonFiles = dirRetrieved.filter((file) => path.extname(file).toLowerCase() === '.json') //Filter all the json files in the desired dir.

  const result = await Promise.all(
    jsonFiles.map(async (file): Promise<any[]> => {
      const newRowPromises: any[] = []

      const newListActRowsPromises: {}[] = []
      const newListAmeRowsPromises: {}[] = []

      const filePath = path.join(directoryPath, dirName, file)
      // Read the JSON file
      const jsonData = fs.readFileSync(filePath, 'utf8')

      // Parse the JSON data
      const jsonContent = JSON.parse(jsonData)
      for (const key2 in jsonContent['data']) {
        const newRow: any = {}
        const value = jsonContent['data'][key2]
        let created_at
        let updated_at

        if (value['created_at']) {
          created_at = DateTime.fromISO(value['created_at']['__time__'])
        } else {
          created_at = DateTime.now()
        }

        if (value['modified_at']) {
          updated_at = DateTime.fromISO(value['modified_at']['__time__'])
        } else {
          updated_at = DateTime.now()
        }

        switch (dirName) {
          case '1_activity':
            newRow.createdAt = created_at
            newRow.isHidden = value['is_hidden']
            newRow.updatedAt = updated_at
            newRow.image_url = await base64ToImage(value['image_base64'], 'activity', key2)
            newRow.name = value['name']
            newRow.description = value['id'] // Temporary assigned with id from firestore
            newRowPromises.push(newRow)
            break
          case '2_amenity':
            newRow.createdAt = created_at
            newRow.updatedAt = updated_at
            newRow.isHidden = value['is_hidden']
            newRow.fontIconName =
              key2 == 'diaper_changing_mat' ? 'diaper-changing' : key2.replace(/_/g, '-')
            newRow.name = value['name']
            newRow.description = value['id'] // Temporary assigned with id from firestore
            newRowPromises.push(newRow)
            break
          // case '3_listing_types':
          //   newRow.createdAt = created_at
          //   newRow.updatedAt = updated_at
          //   newRow.isHidden = value['is_hidden']
          //   newRow.index = value['index']
          //   newRow.name = value['name']
          //   newRow.description = value['id'] // Temporary assigned with id from firestore
          //   newRowPromises.push(newRow)
          //   break
          case '4_regions':
            newRow.createdAt = created_at
            newRow.isHidden = value['is_hidden']
            newRow.updatedAt = updated_at
            newRow.image_url = await base64ToImage(value['image_base64'], 'region', key2)
            newRow.name = value['name']
            newRow.description = value['id'] // Temporary assigned with id from firestore
            newRowPromises.push(newRow)
            break
          // case '5_user_types':
          //   newRow.createdAt = created_at
          //   newRow.isHidden = value['is_hidden']
          //   newRow.updatedAt = updated_at
          //   newRow.name = value['name']
          //   newRow.description = value['id']
          //   newRowPromises.push(newRow)
          //   break
          case '6_users':
            let allKey: any[] = []
            Object.keys(value).forEach((eachKey) => {
              if (eachKey != '__collections__') {
                allKey.push(eachKey)
              }
            })

            const isAllNullvalue = allKey.every((key) => !value[key] == true)
            if (isAllNullvalue) {
              console.log(isAllNullvalue)
              console.log(
                'User does not have any value but only user id, thus skipped insert, user id: ' +
                  key2
              )
              break
            }

            // Position row (Created here to immediately get id)
            if (value['latitude'] != undefined && value['longitude'] != undefined) {
              if (!(value['latitude'] == 0 && value['longitude'] == 0)) {
                // If latitude and logitude both is not 0, according to staff, both 0 is not intended, should be removed.
                try {
                  // Validate exisitng position
                  const newGeoHash = ngeohash.encode(value['latitude'], value['longitude'], 20)
                  const isExistPosition = await Position.query()
                    .whereRaw(
                      `ST_Equals(coordinate, POINT(${value['longitude']}, ${value['latitude']}))`
                    )
                    .first()

                  if (isExistPosition) {
                    // console.log(
                    //   "A user's Position already exist, no need to create new, use back existing positon id."
                    // )
                    newRow.positionId = isExistPosition.id
                  } else {
                    const newPositionId = uuidv4()
                    await Database.rawQuery(
                      `INSERT INTO positions (id, geo_hash, coordinate, is_hidden) VALUES ('${newPositionId}','${newGeoHash}', POINT(${value['longitude']}, ${value['latitude']}), ${value['is_hidden']})`
                    )

                    newRow.positionId = newPositionId
                  }
                } catch (err) {
                  // console.log(err)
                  // console.log('position failed to create, user: ' + key2 + ' are skipped')
                  break
                }
              }
            }

            // let userTypeExist: any
            let userType: UserType

            if (
              [
                'n8MX98QhAIcw6VXls6HSqunIJhN2',
                'HWOmzOFGPxYeFoApv8OaAg7D9dX2',
                '4ljIGKWTw1fNc3OyqJuf0gIQ5hj1',
              ].includes(key2)
            ) {
              userType = UserType.admin
              // userTypeExist = await UserType.findBy('description', 'admin') // Validate type exist during insert, set as admin, if this found any one of these 3 users
            } else {
              userType = value['type'] == UserType.company ? UserType.company : UserType.user
              // userTypeExist = await UserType.findBy(
              //   'description',
              //   value['type'] ? value['type']['id'] : ''
              // ) // Validate type exist during insert
            }

            newRow.firestoreId = key2

            newRow.authProvider = value['auth_provider']
            newRow.birthday = value['birthday']
            newRow.childrenBirthdays = value['children_birthdays']
            newRow.companyName = value['company_name'] ? value['company_name'] : null
            newRow.countryCode = value['country_code']
            newRow.countryDialCode = value['country_dial_code']
            newRow.countryName = value['country_name']
            newRow['created_at'] ? (newRow.createdAt = created_at) : null
            newRow.emailAddress = value['email_address'] ? value['email_address'] : null
            newRow.firstName = value['first_name']
            newRow.gender = value['gender'] == 'Male' ? UserGender.male : UserGender.female
            newRow.isAdminVerified = value['is_admin_verified']
            newRow.isEmailAddressVerified = value['is_email_address_verified']
            newRow.isGomamaVerified = value['is_gomama_verified']
            newRow.isHidden = value['is_hidden']
            newRow.isMobileNumberVerified = value['is_mobile_number_verified']
            newRow.isPassportVerified = value['is_passport_verified']
              ? value['is_passport_verified']
              : false
            newRow.lastName = value['last_name']
            newRow.mobileNumber = value['mobile_number'] ? value['mobile_number'] : null
            value['modified_at'] ? (newRow.updatedAt = updated_at) : null
            newRow.passportNumber = value['passport_number']
            newRow.photoUrl = value['photo_base64']
              ? await base64ToImage(value['photo_base64'], 'user_profile', key2)
              : value['photo_url']
            newRow.shareCode = value['share_code']
            newRow.singpassNonce = value['singpass_nonce']
            newRow.singpassSecret = value['singpass_secret']
            newRow.singpassState = value['singpass_state']
            newRow.timezone = value['timezone']
            // userTypeExist ? (newRow.typeId = userTypeExist.id) : null // Some value does not have userType for unknown reason
            newRow.userType = userType
            newRow.username = value['username'] ? value['username'].trim() : undefined
            // random password for the users
            newRow.password = generateRandomCode(16)
            // Create New User to get User ID immediately

            // These ID are duplicated user email from old system, likely test users.
            if (
              ![
                '07q4c8GgJGg1Qgu9R82IrYK3tLG3',
                'BrX8MbRGbaZt1ccmXjCjaTiDVTG2',
                'mU0BGdgjCBVzOyO30XDdhwPNXo93',
                'PbierRkAd6ZvBxz2OvXNb3yYCRn2',
                '2rN0CiQ3yhfGkeo6GLjSVJAKe5q2',
                'rveVgqDAg3QG0em9CaY9ZXeh6PG3',
                'J9byuVwCF2U9HPY0o6CI82izKqI3',
                '0O0kuuZDElY01QhHgEm08mly55I3', // <EMAIL> (email only account), we use the one with full mobile number only.
              ].includes(key2)
            ) {
              const newUser = await User.create({ ...newRow })

              // If this 3 values is not empty means user did submit singpass for verification
              if (value['singpass_state'] && value['singpass_secret'] && value['singpass_nonce']) {
                // Create Singpass Info
                await UserVerificationRequest.create({
                  singpassMobileNumber: value['singpass_full_mobile_number'],
                  singpassNric: (newRow.nric = value['nric_fin_last4']),
                  userId: newUser.id,
                  type: UserVerificationType.singpass,
                  status: value['is_singpass_verified']
                    ? UserVerificationStatus.approve
                    : UserVerificationStatus.reject,
                  reason: 'old firebase singpass info',
                  reviewedAt: DateTime.now(),
                })
              }
            }
            newRowPromises.push(newRow)
            break
          case '7_listings':
            numRows += 1
            newRow.firestoreId = key2

            // Check if region exist
            if (value['region']) {
              const regionExist = await Region.findBy('description', value['region']['id'])
              if (regionExist) {
                newRow.regionId = regionExist.id
              } else {
                !nonExistRegionValue.includes(value['region']['id'])
                  ? nonExistRegionValue.push(value['region']['id'])
                  : null
                // console.log(
                //   'A region does not exist in region table for key2 : ' +
                //     key2
                // )
              }
            } else {
              // console.log('Region does not exist for key2 : ' + key2)
            }

            //Listing Type ID
            if (value['type'] && value['type']['id']) {
              // newRow.listingType = value['type']['id']
              newRow.listingType = 'care'

              if (!isEmpty(value['api_key'])) {
                newRow.listingType = 'gomama'
              }

              // const listingTypeExist = await ListingType.findBy('description', value['type']['id'])

              // if (listingTypeExist) {
              //   newRow.typeId = listingTypeExist.id
              // } else {
              //   // console.log(
              //   //   'A listing type id does not exist in listing type table for key2: ' + key2
              //   // )
              // }
            } else {
              // console.log('Type/Type id column does not exist for key2: ' + key2)
            }

            // Position row (Created here to immediately get id)
            if (value['position']) {
              try {
                // Validate exisitng position
                const newGeoHash = ngeohash.encode(
                  value['position']['geopoint']['__lat__'],
                  value['position']['geopoint']['__lon__'],
                  20
                ) // Note: The old production data have precision of 10 geohash (which is not enough, there exist different coordinates but with same geohash), old production data is ignored and replaced with precision of 20 geohash, .
                const isExistPosition = await Position.query()
                  .whereRaw(
                    `ST_Equals(coordinate, POINT(${value['position']['geopoint']['__lon__']}, ${value['position']['geopoint']['__lat__']}))`
                  )
                  .first()

                if (isExistPosition) {
                  // console.log(
                  //   'Position already exist, no need to create new, use back existing positon id.'
                  // )
                  newRow.positionId = isExistPosition.id
                } else {
                  const newPositionId = uuidv4()
                  await Database.rawQuery(
                    `INSERT INTO positions (id, geo_hash, coordinate, is_hidden) VALUES ('${newPositionId}','${newGeoHash}', POINT(${value['position']['geopoint']['__lon__']}, ${value['position']['geopoint']['__lat__']}), ${value['is_hidden']})`
                  )

                  newRow.positionId = newPositionId
                }
              } catch (err) {
                // console.log(err)
                //
                console.log('position failed to create, listing: ' + key2 + ' are skipped')
                break
              }
            }
            newRow.addressName = value['address_name']
            newRow.apiKey = value['api_key']
            newRow.contactNumber = value['contact_number']
            newRow.countryDialCode = value['country_dial_code']
            newRow.createdAt = created_at
            newRow.description = value['description']
            newRow.diaperChangingMatType = value['diaper_changing_mat_type']
            newRow.fullAddress = value['full_address']
            newRow.humidity = value['humidity']
            //newRow.isDoorOpened = value['is_door_opened']
            //newRow.isFanOn = value['is_fan_on']
            newRow.isHidden = value['is_hidden']
            //newRow.isLedLightOn = value['is_led_light_on']
            //newRow.isLockConnected = value['is_lock_connected']
            //newRow.isLockInstalled = value['is_lock_installed']
            newRow.doorIsLockable = value['is_lockable']
            //newRow.isLocked = value['is_locked']
            newRow.status = value['is_occupied']
              ? ListingStatus.occupied
              : value['is_disinfecting']
              ? ListingStatus.disinfecting
              : ListingStatus.idle
            newRow.isUsageExtendable = value['is_usage_extendable']
            //newRow.isUvcLampOn = value['is_uvc_lamp_on']
            newRow.isVerified = value['is_verified']
            newRow.keywords = value['keywords']
            newRow.lockBluetoothAdminKey = value['lock_bluetooth_admin_key']
            newRow.lockId = value['lock_id']
            newRow.lockMasterPin = value['lock_master_pin']
            newRow.maxNumberOfUsageExtensions = value['max_number_of_usage_extensions']
            newRow.updatedAt = updated_at
            newRow.maxNumberOfUsageExtensions = value['max_number_of_usage_extensions']
            newRow.name = value['name']
            newRow.numberOfDiaperChangingMats = value['number_of_diaper_changing_mats']
            newRow.numberOfPrivateFeedingRooms = value['number_of_private_feeding_rooms']
            newRow.openingHours = value['opening_hours']

            if (value['owner']) {
              if (value['owner']['company_name']) {
                newRow.companyName = value['owner']['company_name']
              }
            } else {
              console.log(key2 + ' listing does not have company name')
            }
            newRow.piId = value['pi_id']
            newRow.piLastUpdated =
              value['pi_last_updated'] && value['pi_last_updated']['__time__']
                ? DateTime.fromISO(value['pi_last_updated']['__time__'])
                : DateTime.now()
            newRow.postalCode = value['postal_code']
            newRow.temperature = value['temperature']
            newRow.usageDurations = value['usage_durations']
            newRow.usageExtensionDurations = value['usage_extension_durations']

            // New Listing Created here to immediately get UUID generated
            const newListingCreated = await Listing.create({ ...newRow })

            // Listing File row
            if (value['image_base64'] || value['image_url']) {
              const newImageUrl = value['image_base64']
                ? await base64ToImage(value['image_base64'], 'listing', newListingCreated.id, true)
                : value['image_url'].split('&_gl')[0]

              await ListingFile.create({
                listingId: newListingCreated.id,
                imageUrl: newImageUrl,
                isMain: true, //Only one image for each listing in old version (so the image here for each listing should be main image)
                isApproved: true,
                isHidden: false,
              }) // Defaulted to true here because its migrated from production, the reviewer and uploaded is unknown because old version does not record that.
            }

            // Listing Activities row
            if (value['activities']) {
              for (const activity of value['activities']) {
                if (activity) {
                  const activityExist = await Activity.findBy('description', activity['id'])
                  if (activityExist) {
                    newListActRowsPromises.push({
                      activityId: activityExist.id,
                      listingId: newListingCreated.id,
                      isHidden: value['is_hidden'],
                    })
                  } else {
                    nonExistActivitytValue.includes(activity['id'])
                      ? null
                      : nonExistActivitytValue.push(activity['id'])
                    // console.log(
                    //   'In Activity arr, an activity does not exist in activity table for key2 : ' +
                    //     key2
                    // )
                  }
                } else {
                  // console.log('Activity is null, activity : ' + activity)
                }
              }
            } else {
              // console.log('Activity arr does not exist for key2 : ' + key2)
            }

            // Listing Amenities row
            if (value['amenities']) {
              for (const amenity of value['amenities']) {
                if (amenity) {
                  const amenityExist = await Amenity.findBy('description', amenity['id'])
                  if (amenityExist) {
                    newListAmeRowsPromises.push({
                      amenityId: amenityExist.id,
                      listingId: newListingCreated.id,
                      isHidden: value['is_hidden'],
                    })
                  } else {
                    nonExistAmenitytValue.includes(amenity['id'])
                      ? null
                      : nonExistAmenitytValue.push(amenity['id'])
                    // console.log(
                    //   'In Amenity arr, an Amenity does not exist in amenity table for key2 : ' +
                    //     key2
                    // )
                  }
                } else {
                  // console.log('Amenity is null, amenity : ' + amenity)
                }
              }
            } else {
              // console.log('Amenity arr does not exist for key2 : ' + key2)
            }
            break
          case '8_sessions':
            numRows += 1
            const newSession = new Session()
            const newListingRating = new ListingRating()
            // Check if user id exist
            if (value['user'] && value['user']['id']) {
              const associatedUser = await User.findBy('firestore_id', value['user']['id'])
              if (associatedUser) {
                newSession.userId = associatedUser.id
              } else {
                // console.log(
                //   `A session's associated user id is not found from user table, session id: ${value['id']}, user id: ${value['user']['id']}`
                // ) // skip insert if user id not found
                break
              }
            } else {
              // console.log(
              //   `A session's user is null, session id: ${value['id']}`
              // ) // skip insert if user id not found
              break
            }

            // Check if listing id exist
            if (value['listing'] && value['listing']['id']) {
              const associatedlisting = await Listing.findBy('firestore_id', value['listing']['id'])
              if (associatedlisting) {
                newListingRating.listingId = associatedlisting.id
                newSession.listingId = associatedlisting.id
              } else {
                // console.log(
                //   `A session's associated listing id is not found from listing table, session id: ${value['id']}`
                // ) // skip insert if listing id not found
                break
              }
            } else {
              // console.log(
              //   `A session's associated listing id is not found from listing table, session id: ${value['id']}`
              // ) // skip insert if listing id not found
              break
            }

            await Database.transaction(async (trx) => {
              newSession.firestoreId = value['id']
              newSession.lockCustomPin = value['lock_custom_pin']
              newSession.createdAt = DateTime.fromISO(value['created_at']['__time__'])
              newSession.lockOneTimePin = value['lock_one_time_pin']
              newSession.lockDailyPin = value['lock_daily_pin']
              newSession.lockHourlyPin = value['lock_hourly_pin']
              newSession.lockBluetoothGuestKey = value['lock_bluetooth_guest_key']
              newSession.numberOfUsageExtensions = value['number_of_usage_extensions']
              newSession.startedAt = DateTime.fromISO(value['start_date_time']['__time__'])
              newSession.expectedEndedAt = DateTime.fromISO(value['end_date_time']['__time__'])
              newSession.actualEndedAt = DateTime.fromISO(
                value['start_date_time']['__time__']
              ).plus({ minutes: value['actual_usage_duration'] as number })
              newSession.updatedAt = DateTime.fromISO(value['modified_at']['__time__'])

              newSession.useTransaction(trx)
              const newSessionCreated = await newSession.save()
              // Listing Rating row (Created here)
              newListingRating.appRating = value['app_rating']
              newListingRating.experienceRating = value['experience_rating']
              newListingRating.listingRating = value['listing_rating']
              newListingRating.review = value['review']
              newListingRating.sessionId = newSessionCreated.id

              newListingRating.useTransaction(trx)
              await newListingRating.save()

              // console.log('New session and listing created successfully : ' + numRows)
            })

            break
          case '9_listing_ratings':
            numRows += 1
            // Check if the same listing rating already being inserted into Listing Rating table during 8_session case
            // The listing rating id use the session id as primary key in firestore (Means for each session there can only be one listing rating)
            if (value['session'] && value['session']['id']) {
              const associatedSession = await Session.findBy(
                'firestore_id',
                value['session']['id'] // Current listing's session id
              )

              if (associatedSession) {
                const existingListingRating = await ListingRating.findBy(
                  'session_id',
                  associatedSession.id
                )

                if (existingListingRating) {
                  // Skip insert listing rating for same session.
                  // console.log('Listing rating for this session already exist, skip insert, session id: ' + existingListingRating.sessionId)
                  break
                }
              }
            } else {
              //skip insert, because session/session id is null
              break
            }

            // Check if listing id exist
            if (value['listing'] && value['listing']['id']) {
              const associatedlisting = await Listing.findBy('firestore_id', value['listing']['id'])
              if (associatedlisting) {
                newRow.listingId = associatedlisting.id
              } else {
                // console.log(
                //   `A listing rating's associated listing id is not found from listing table, listing rating id: ${value['id']}`
                // ) // skip insert if listing id not found
                break
              }
            } else {
              // console.log(
              //   `A listing rating's associated listing id is not found from listing table, listing rating id: ${value['id']}`
              // ) // skip insert if listing id not found
              break
            }

            // Check if session id exist
            if (value['session'] && value['session']['id']) {
              const associatedSession = await Session.findBy('firestore_id', value['session']['id'])
              if (associatedSession) {
                newRow.sessionId = associatedSession.id
              } else {
                // console.log(
                //   `A listing rating's associated session id is not found from session table, listing rating id: ${value['id']}`
                // ) // skip insert if listing id not found
                break
              }
            } else {
              // console.log(
              //   `A listing rating's associated session id is not found from session table, listing rating id: ${value['id']}`
              // ) // skip insert if listing id not found
              break
            }

            newRow.appRating = value['app_rating']
            newRow.experienceRating = value['experience_rating']
            newRow.listingRating = value['listing_rating']
            newRow.review = value['review']
            newRow.isHidden = value['isHidden']
            newRow.createdAt = created_at
            newRow.updatedAt = updated_at

            newRowPromises.push(newRow)
            break
          case '10_user_listings':
            // Check if listing id exist
            if (value['listing'] && value['listing']['id']) {
              const associatedlisting = await Listing.findBy('firestore_id', value['listing']['id'])
              if (associatedlisting) {
                newRow.listingId = associatedlisting.id
              } else {
                !nonExistListing.includes(value['listing']['id'])
                  ? nonExistListing.push(value['listing']['id'])
                  : null
                // console.log(
                //   `A user listing's associated listing id is not found from listing table, user listing id: ${value['id']}`
                // ) // skip insert if listing id not found
                break
              }
            } else {
              // console.log(
              //   `A user listing's associated listing id is not found from listing table, user listing id: ${value['id']}`
              // ) // skip insert if listing id not found
              break
            }

            // Check if user id exist
            if (value['user'] && value['user']['id']) {
              const associatedUser = await User.findBy('firestore_id', value['user']['id'])
              if (associatedUser) {
                newRow.userId = associatedUser.id
              } else {
                !nonExistUser.includes(value['user']['id'])
                  ? nonExistUser.push(value['user']['id'])
                  : null
                // console.log(
                //   `A user listing's associated user id is not found from user table, user listing id: ${value['id']}`
                // ) // skip insert if user id not found
                break
              }
            } else {
              // console.log(
              //   `A user listing's associated user id is not found from user table, user listing id: ${value['id']}`
              // ) // skip insert if user id not found
              break
            }

            newRow.createdAt = created_at
            newRow.updatedAt = updated_at

            newRowPromises.push(newRow)
            break
          default:
            break
        }
      }

      if (dirName == '7_listings') {
        console.log(dirName)
        console.log('Non existing Amenity Value From Listing')
        console.log(nonExistAmenitytValue)

        console.log('Non existing Activity Value From Listing')
        console.log(nonExistActivitytValue)

        console.log('Non existing Region Value From Listing')
        console.log(nonExistRegionValue)

        console.log('Listing Row: ' + numRows)
        console.log('Listing Activity Row: ' + newListActRowsPromises.length)
        console.log('Listing Amenity Row: ' + newListAmeRowsPromises.length)
        newRowPromises[0] = newListActRowsPromises
        newRowPromises[1] = newListAmeRowsPromises
        console.log(newRowPromises.length)

        return newRowPromises
      }

      console.log(dirName)

      if (['8_sessions', '9_listing_ratings'].includes(dirName)) {
        console.log(numRows)

        if (dirName == '9_listing_ratings') {
          console.log(
            'Listing rating row that are not already exist (new row created from listing rating json file): ' +
              newRowPromises.length
          )
        }
      } else {
        if (dirName == '10_user_listings') {
          console.log('Non existing listing from user listing')
          console.log(nonExistListing)

          console.log('Non existing user from user listing')
          console.log(nonExistUser)
        }

        console.log(newRowPromises.length)
      }

      return newRowPromises
    })
  )

  return result[0]
}
