import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'
import UserVerificationRequest from 'App/Models/UserVerificationRequest'
import { UserType } from 'Contracts/user_type'
import {
  UserVerificationStatus,
  UserVerificationType,
} from 'Contracts/user_verification_request_type'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return
    //------------------------------------Gomama Verification//------------------------------------
    //Mock user to send gomama verification request.
    await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'toReqGomamaVerifyUser1',
      userType: UserType.user,
      isHidden: false,
    })

    // Mock User already create a request for gomama verification
    const userRequestGomamaVerify = await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'gomamaVeriReqUser1',
      mobileNumber: '93103429',
      userType: UserType.user,
      isHidden: false,
    })

    await UserVerificationRequest.create({
      type: UserVerificationType.gomama,
      userId: userRequestGomamaVerify.id,
      singpassMobileNumber: '+651696238',
      singpassNric: '8I9J',
      status: UserVerificationStatus.pending,
    })

    // Mock User already verified by gomama
    await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'gomamaVerifiedUser1',
      userType: UserType.user,
      isHidden: false,
      isGomamaVerified: true,
    })

    ////------------------------------------Singpass verification//------------------------------------
    //Mock user to send singpass verification request.
    await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'toReqSingpassVerifyUser1',
      userType: UserType.user,
      isHidden: false,
    })

    // Mock User already create a request for singpass verification
    const userRequestSingpassVerify = await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'singpassVeriReqUser1',
      mobileNumber: '83749275',
      userType: UserType.user,
      isHidden: false,
    })

    await UserVerificationRequest.create({
      type: UserVerificationType.singpass,
      userId: userRequestSingpassVerify.id,
      singpassMobileNumber: '+652398472',
      singpassNric: '8I9J',
      status: UserVerificationStatus.pending,
    })

    // Mock User already verified by singpass
    await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'singpassVerifiedUser1',
      userType: UserType.user,
      isHidden: false,
      isSingpassVerified: true,
    })
  }
}
