import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ListingActivity from 'App/Models/ListingActivity'
import ListingAmenity from 'App/Models/ListingAmenity'
import { dirName, importData } from 'App/utils/dataImport'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (Env.get('SEEDER_RUN_TEST_CASE')) return
    const newRowArr: any[] = await importData(dirName.listings)

    //Listing rows created in the data import case 7_listings to immediately get id for listing activity, listing amenity and Listing File.
    await ListingActivity.createMany(newRowArr[0]) // Create listing activity rows
    await ListingAmenity.createMany(newRowArr[1]) // Create listing amenity rows
  }
}
