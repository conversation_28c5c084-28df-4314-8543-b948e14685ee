import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import User from 'App/Models/User'
import { UserType } from 'Contracts/user_type'
import { UserGender } from 'Contracts/users'
import { DateTime } from 'luxon'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return

    // Normal Condition User
    await User.createMany([
      // Admin
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        userType: UserType.admin,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        firstName: 'AISHA',
        lastName: 'NUR',
        photoUrl: 'HWOmzOFGPxYeFoApv8OaAg7D9dX2.png',
        shareCode: 'ex7ljhd1',
        gender: UserGender.female,
        childrenBirthdays: [DateTime.fromFormat('2018-08-02', 'yyyy-MM-dd')],
        mobileNumber: '88082940',
        countryName: 'Singapore',
        countryCode: 'SG',
        countryDialCode: '65',
        timezone: 'Asia/Singapore',
        authProvider: 'phone',
        isAdminVerified: false,
        isEmailAddressVerified: true,
        isMobileNumberVerified: true,
        isPassportVerified: false,
        isGomamaVerified: false,
        isHidden: false,
        password: 'abcd1234',
        userType: UserType.admin,
        firestoreId: 'HWOmzOFGPxYeFoApv8OaAg7D9dX2',
        singpassNonce:
          'U3ohKMzsJDmtJd1zs6J1tUUalkHxj12zmXxqYIoqPv2meh5J5ci9rHWiAmjDqR87liN8iamFgG5FimKq09R7Btg8jGBcXbplkZNb0jVozvzK7L0kbi2y6Ltc7k2Zu42qLy1SwnQFAlMosZpHBgqmJjz1ulbDXpXpLjkUwbsO41RS1FK4eNfjJoRlXkcJHAl2KX28MtdLvXjWzJWqjXXgGNTl6DsJrBq2UGCAaONbWwAIkM8wdPUkEYDMurgvEUm',
        singpassState:
          'ZIUFvBYg0b5sMPTjCpPH8h6GhiKmb8tlPIKcUwUWSTaMjK8TZwLBMAxP4BFe5WRFPnjVvJkx1OwkoNw35pbI2SCNpc5DjEGO3uPfz5PVDY55OHUmuvs6FSq3Poio1Gb62FPwzXNvBsV1XKsMU2SBslVk6UPRv6Vags4votZvkNvTsgUsLG6dUCyUYWqhzFmG0rHCsq3T483BcaODIqDiRtOOhbKsT4bISLQVZKUvgBtzQNq2IJVdoGUguUhDJay',
        singpassSecret:
          'SDrO2JPpsusuMjGkdQmzmXdsHcqk6rLcsFh1nNhOhLpuDVzgpQSkoIr7rEDeCHAXADNdEkMxv97rM668iLHLowcr5OBgEeiDWIBpvIwzSzviVcUTCsHG3gjprZ9h4JkZ2olKp5hgGku5DGY7eMSBylW5o56mRKInKjXLVCgIi1dkeczKCrVnm4WC3pmUuHSAfUnfwtwqekhNEhTjg0nS2h7YxI9kIvocqgNR6stPCxs3cFbQFB3sW1qsVipJpmI',
        createdAt: DateTime.fromFormat('2024-06-25 18:28:43', 'yyyy-MM-dd HH:mm:ss'),
        updatedAt: DateTime.fromFormat('2023-10-13 13:23:09', 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        mobileNumber: '6512345678',
        password: 'abcd1234',
        username: 'adminUser3',
        userType: UserType.admin,
        isHidden: false,
      },
      // User
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser1',
        userType: UserType.user,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser2',
        userType: UserType.user,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser3',
        userType: UserType.user,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser4',
        userType: UserType.user,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser5',
        userType: UserType.user,
        isHidden: false,
      },
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'normalUser6',
        userType: UserType.user,
        isHidden: false,
      },
      // Normal user with phone account
      {
        mobileNumber: '**********',
        password: 'abcd1234',
        username: 'normalUser7',
        userType: UserType.admin,
        isHidden: false,
      },
      // To be updated user
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'updateTestUser1',
        userType: UserType.user,
        isHidden: false,
      },
      // To be soft deleted user
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'tobeSoftDeletedUser1',
        userType: UserType.user,
        isHidden: false,
      },
      // To be hard deleted user
      {
        emailAddress: '<EMAIL>',
        password: 'abcd1234',
        username: 'tobeHardDeletedUser1',
        userType: UserType.user,
        isHidden: false,
      },
    ])

    // Special Condition User
    await User.createMany([
      // Hidden Admin
      {
        emailAddress: '<EMAIL>',
        username: 'hiddenAdminUser',
        password: 'abcd1234',
        userType: UserType.admin,
        isHidden: true,
      },
      // Hidden User
      {
        emailAddress: '<EMAIL>',
        username: 'hiddenUser',
        password: 'abcd1234',
        userType: UserType.user,
        isHidden: true,
      },
      // Soft Deleted Admin
      {
        emailAddress: '<EMAIL>',
        username: 'softDeletedAdmin',
        password: 'abcd1234',
        userType: UserType.admin,
        deletedAt: DateTime.now(),
      },
      // Soft Deleted User
      {
        emailAddress: '<EMAIL>',
        username: 'softDeletedUser',
        password: 'abcd1234',
        userType: UserType.admin,
        deletedAt: DateTime.now(),
      },
      // To be restore soft deleted user
      {
        emailAddress: '<EMAIL>',
        username: 'tobeRestoreSoftDeletedUser1',
        password: 'abcd1234',
        userType: UserType.admin,
        deletedAt: DateTime.now(),
      },
    ])
  }
}
