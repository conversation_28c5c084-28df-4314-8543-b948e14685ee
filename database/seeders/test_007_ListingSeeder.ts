import Database from '@ioc:Adonis/Lucid/Database'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import Position from 'App/Models/Position'
import Region from 'App/Models/Region'
import User from 'App/Models/User'
import { ListingType } from 'Contracts/listing_type'
import { DateTime } from 'luxon'
import { v4 as uuidv4 } from 'uuid'
import * as ngeohash from 'ngeohash'
import ListingActivity from 'App/Models/ListingActivity'
import Activity from 'App/Models/Activity'
import Amenity from 'App/Models/Amenity'
import ListingAmenity from 'App/Models/ListingAmenity'
import ListingFile from 'App/Models/ListingFile'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return

    const adminUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const normalUser = await User.findByOrFail('emailAddress', '<EMAIL>')
    const regions = await Region.query()
    const activities = await Activity.query()
    const amenities = await Amenity.query()

    // Position 1
    const lon1 = 103.7877
    const lat1 = 1.299739
    const geohash = ngeohash.encode(lon1, lat1, 20)
    const newPositionId = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId, geohash, lon1, lat1]
    )
    const position1 = await Position.findOrFail(newPositionId)

    // Position 2
    const lon2 = 103.************
    const lat2 = 1.37222643291526
    const geohash2 = ngeohash.encode(lon2, lat2, 20)
    const newPositionId2 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId2, geohash2, lon2, lat2]
    )
    const position2 = await Position.findOrFail(newPositionId2)

    // Position 3
    const lon3 = 103.************
    const lat3 = 1.37261323056833
    const geohash3 = ngeohash.encode(lon3, lat3, 20)
    const newPositionId3 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId3, geohash3, lon3, lat3]
    )
    const position3 = await Position.findOrFail(newPositionId3)

    // Position 4
    const lon4 = 103.************
    const lat4 = 1.29340197332238
    const geohash4 = ngeohash.encode(lon4, lat4, 20)
    const newPositionId4 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId4, geohash4, lon4, lat4]
    )
    const position4 = await Position.findOrFail(newPositionId4)

    // Position 5
    const lon5 = 103.************
    const lat5 = 1.30410702010224
    const geohash5 = ngeohash.encode(lon5, lat5, 20)
    const newPositionId5 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId5, geohash5, lon5, lat5]
    )
    const position5 = await Position.findOrFail(newPositionId5)

    // Position 6
    const lon6 = 103.************
    const lat6 = 1.30736583933813
    const geohash6 = ngeohash.encode(lon6, lat6, 20)
    const newPositionId6 = uuidv4()
    await Database.rawQuery(
      'INSERT INTO positions (id, geo_hash, coordinate) VALUES (?,?, POINT(?, ?))',
      [newPositionId6, geohash6, lon6, lat6]
    )
    const position6 = await Position.findOrFail(newPositionId6)

    await Listing.createMany([
      {
        positionId: position1.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Go!mama Pod SKCC001',
        listingType: ListingType.gomama,
        companyName: `People's Association`,
        addressName: 'Ground Floor, Near main entrance of Sengkang CC',
        description: 'Ground Floor, Near main entrance of Sengkang CC',
        fullAddress: '2 Sengkang Square, Sengkang Community Hub, Singapore 545025',
        contactNumber: '545025',
        keywords: [
          '545025',
          'breastfeeding',
          'skcc',
          'care',
          'sengkang',
          'breastfeeding',
          'cc',
          'pa',
          'community centre',
          'community club',
          'community',
          "people's association",
          'sengkang square',
          'sengkang north',
          'ccmc',
          'ccc',
          'wec',
          'go!mama',
        ],
        postalCode: '545025',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 0,
        countryDialCode: '65',
        openingHours: '24 Hours',
        piId: '000000009eccd983',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          '8yTVmRsA2CXg8jAc6SW9pvoMxRxw6hhYsV7x2bxCjTejERvbkteCtQHomWgRNEsgiAq2BJeNGmBCuJ9ueeKsraGjGpz28B9SnNUXBAxv2yUYjDe6ExnirXpHXUnCBeb2',
        lockId: 'IGB41547313d',
        lockMasterPin: '03647573',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2107062044251335233',
      },
      {
        positionId: position2.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Go!mama Pod 106',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC)`,
        addressName: 'Palawan Beach Taxi Point, In front of FOC Sentosa',
        description: 'Palawan Beach Taxi Point, In front of FOC Sentosa',
        fullAddress: '110 Tanjong Beach Walk, Singapore 098943',
        contactNumber: '',
        keywords: [
          'care',
          'go!mama',
          'breastfeeding',
          'sentosa',
          'sdc',
          'pod',
          'Palawan Beach Taxi Point',
          'tanjong',
          'tanjong beach',
          '098943',
          '24hours',
          '24 hours',
          '24hr',
          '24h',
          '24 hrs',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098943',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 0,
        countryDialCode: '65',
        openingHours: '24 Hours',
        piId: '0000000032085abe',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          'DJditLHhw7YhP9kFGMvw9u4dnfXn4cnUquvHKnVeVfBCciKoRgAxEpf2Bh2ZSTPeCq2tLAyTcnWCHkDthEDWLztyHCbn2CCVZxmpUAQaFe7V2C34vmAPV4i65sgcvPoz',
        lockId: 'IGB41562ec5a',
        lockMasterPin: '22202002',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2110221540094412279',
      },
      {
        positionId: position3.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Update Listing 1',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC)`,
        addressName: 'Level 1, Near Imbiah Station Exit',
        description: 'Level 1, Near Imbiah Station Exit',
        fullAddress: '10 Beach View, Singapore 098588',
        contactNumber: '',
        keywords: [
          'care',
          'breastfeeding',
          'diaper changing',
          'sentosa',
          'sdc',
          'nursing room',
          'Imbiah Station',
          'imbiah',
          '098588',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098588',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am–10pm',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          '8yTVmRsA2CXg8jAc6SW9pvoMxRxw6hhYsV7x2bxCjTejERvbkteCtQHomWgRNEsgiAq2BJeNGmBCuJ9ueeKsraGjGpz28B9SnNUXBAxv2yUYjDe6ExnirXpHXUnCBABC',
        lockId: 'IGB415c04519',
        lockMasterPin: '64747216',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2107062044251338888',
      },
      {
        positionId: position4.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Update Listing 2',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC)`,
        addressName: 'Resorts World Station Level 1, Near Lift',
        description: 'Resorts World Station Level 1, Near Lift',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: [
          'care',
          'go!mama',
          'breastfeeding',
          'sentosa',
          'sdc',
          'pod',
          'Resorts World Station',
          'rws station',
          '098138',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '0000000022e4f768',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          'ac7VAhnbjbwi99sAXrEDwc3Q35pcJgGcmAVbpN9QtyfDgkgkNRsC9ei99uK7K64vazBVSrFpYa3kEtu4ReUpneEDYNwaYSJTgt5sdiLetMKA6Pjm6n8d7YcBFgTFDEjZ',
        lockId: 'IGB407577e8f',
        lockMasterPin: '39838986',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2110261423594320081',
      },
      {
        positionId: position5.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Delete Listing 1',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC)`,
        addressName: 'Resorts World Station Level 1, Near Lift',
        description: 'Resorts World Station Level 1, Near Lift',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: [
          'care',
          'go!mama',
          'breastfeeding',
          'sentosa',
          'sdc',
          'pod',
          'Resorts World Station',
          'rws station',
          '098138',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '000000008fee6f9c',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          'v7yjZJkGkzgdaTjzgTFoqGx3rPQtpiSGwQjkEuR9z3XJghwPDsjYSM2pBNWJLRLCMrRkVffq2tJkTZQHBGnZPQCKU9mSk95GGj4bQSsw832jnZWorMAFNabrF3VZQ8Xn',
        lockId: 'IGB407bd3260',
        lockMasterPin: '32531485',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2110221540216984376',
      },
      {
        positionId: position6.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Unverified Listing 1',
        listingType: ListingType.care,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: [
          'care',
          'go!mama',
          'breastfeeding',
          'sentosa',
          'sdc',
          'pod',
          'Resorts World Station',
          'rws station',
          '098138',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '556677882ehebwbuebabwb293929392982bu32832',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'unverifiedListing1',
      },
      {
        positionId: position5.id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: normalUser.id,
        verifiedBy: adminUser.id,
        name: 'Listing with 0 extension',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC)`,
        addressName: 'Resorts World Station Level 1, Near Lift',
        description: 'Resorts World Station Level 1, Near Lift',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: [
          'care',
          'go!mama',
          'breastfeeding',
          'sentosa',
          'sdc',
          'pod',
          'Resorts World Station',
          'rws station',
          '098138',
          'lactation room',
          'feeding room',
          'room',
          'lactation',
          'feeding',
        ],
        postalCode: '098138',
        usageDurations: [30],
        usageExtensionDurations: [],
        maxNumberOfUsageExtensions: 0,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '000000003bbc0e6c',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey:
          'SDfgD6ekLKEF5oIye0EuAmspFDzLTfXadEm1e9mOnwMDVIC9jQ4MEOCRBFEQO713N75Xi8YBbGiyUjBmg1VBOF2i37sjqLvpnuhe7dKxSLER6oeEXdfg2rQZyFiii46w',
        lockId: 'IGB4073beb99',
        lockMasterPin: '95396280',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: true,
        isHidden: false,
        firestoreId: '2106062054291335224',
      },
    ])

    // Create Listing Activity & Listing Amenites for the above 4 listings
    const newListing1 = await Listing.findByOrFail('name', 'Go!mama Pod SKCC001')
    const newListing2 = await Listing.findByOrFail('name', 'Go!mama Pod 106')
    const newListing3 = await Listing.findByOrFail('name', 'Demo Update Listing 1')
    const newListing4 = await Listing.findByOrFail('name', 'Demo Update Listing 2')

    await ListingActivity.createMany([
      {
        listingId: newListing1.id,
        activityId: activities[Math.floor(Math.random() * activities.length)].id,
      },
      {
        listingId: newListing2.id,
        activityId: activities[Math.floor(Math.random() * activities.length)].id,
      },
    ])

    await ListingAmenity.createMany([
      {
        listingId: newListing1.id,
        amenityId: amenities[Math.floor(Math.random() * amenities.length)].id,
      },
      {
        listingId: newListing2.id,
        amenityId: amenities[Math.floor(Math.random() * amenities.length)].id,
      },
    ])

    // Create Listing Files for the above 4 listings
    await ListingFile.createMany([
      // Listing 1 files
      {
        listingId: newListing1.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2203161530435766626%2Funnamed%20(1).jpg?alt=media&token=af37e29b-b151-49f3-9c57-b93039eb54a7',
        isMain: true,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing1.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing1.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing1.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: false,
        isHidden: false,
        notApprovedReason: 'repeated image',
      },
      // Listing 2 files
      {
        listingId: newListing2.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2203161530435766626%2Funnamed%20(1).jpg?alt=media&token=af37e29b-b151-49f3-9c57-b93039eb54a7',
        isMain: true,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing2.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing2.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing2.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: false,
        isHidden: false,
        notApprovedReason: 'repeated image',
      },
      // Listing 3 files
      {
        listingId: newListing3.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2203161530435766626%2Funnamed%20(1).jpg?alt=media&token=af37e29b-b151-49f3-9c57-b93039eb54a7',
        isMain: true,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing3.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing3.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing3.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: false,
        isHidden: false,
        notApprovedReason: 'repeated image',
      },
      // Listing 4 files
      {
        listingId: newListing4.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2203161530435766626%2Funnamed%20(1).jpg?alt=media&token=af37e29b-b151-49f3-9c57-b93039eb54a7',
        isMain: true,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing4.id,
        uploadedBy: adminUser.id,
        imageUrl:
          'https://makchic.com/wp-content/uploads/2014/08/Tropicana-Gardens-Nursing-Station.jpg',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing4.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: true,
        isHidden: false,
        reviewedBy: adminUser.id,
      },
      {
        listingId: newListing4.id,
        uploadedBy: normalUser.id,
        imageUrl:
          'https://firebasestorage.googleapis.com/v0/b/gomama-prod.appspot.com/o/images%2Flistings%2F2110132144504491100%2Funnamed%20(1).jpg?alt=media&token=2b236bdc-6de9-40dd-9f26-75c1c630b606',
        isMain: false,
        isApproved: false,
        isHidden: false,
        notApprovedReason: 'repeated image',
      },
    ])
  }
}
