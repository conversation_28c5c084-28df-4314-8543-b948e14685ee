import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import User from 'App/Models/User'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return

    const adminUser = await User.findByOrFail('email_address', '<EMAIL>')
    const listing1 = await Listing.findByOrFail('name', 'Go!mama Pod SKCC001')
    const listing2 = await Listing.findByOrFail('name', 'Go!mama Pod 106')

    await UserFavoriteListing.createMany([
      {
        listingId: listing1.id,
        userId: adminUser.id,
      },
      {
        listingId: listing2.id,
        userId: adminUser.id,
      },
    ])
  }
}
