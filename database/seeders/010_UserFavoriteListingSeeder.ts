import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import UserFavoriteListing from 'App/Models/UserFavoriteListing'
import { dirName, importData } from 'App/utils/dataImport'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (Env.get('SEEDER_RUN_TEST_CASE')) return
    const newRowArr: any[] = await importData(dirName.userListings)

    await UserFavoriteListing.createMany(newRowArr)
  }
}
