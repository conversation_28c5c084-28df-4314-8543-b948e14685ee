import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
// import UserType from 'App/Models/UserType'
// import { dirName, importData } from 'App/utils/dataImport'

export default class extends BaseSeeder {
  public async run() {
    // const newRowArr: any[] = await importData(dirName.userTypes)
    // newRowArr.push({
    //   name: 'Admin',
    //   description: 'admin',
    //   isHidden: false,
    // })
    // await UserType.createMany(newRowArr)
  }
}
