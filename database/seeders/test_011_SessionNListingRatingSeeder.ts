import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Listing from 'App/Models/Listing'
import ListingRating from 'App/Models/ListingRating'
import Region from 'App/Models/Region'
import Session from 'App/Models/Session'
import User from 'App/Models/User'
import { generateRandomPosition } from 'App/utils/testUtil'
import { ListingType } from 'Contracts/listing_type'
import { UserType } from 'Contracts/user_type'
import { DateTime } from 'luxon'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return

    const demoUser = await User.create({
      emailAddress: '<EMAIL>',
      password: 'abcd1234',
      username: 'normalUserRating1',
      userType: UserType.user,
      isHidden: false,
    })

    const adminUser = await User.findByOrFail('email_address', '<EMAIL>')

    const randomPosition = await generateRandomPosition(3)
    const regions = await Region.query()

    await Listing.createMany([
      {
        positionId: randomPosition[0].id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: demoUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Listing Rating 1',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'listingDemoRating1',
      },
      {
        positionId: randomPosition[1].id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: demoUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Listing Rating 2',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'listingDemoRating2',
      },
      {
        positionId: randomPosition[2].id,
        regionId: regions[Math.floor(Math.random() * regions.length)].id, // random region
        suggestedBy: demoUser.id,
        verifiedBy: adminUser.id,
        name: 'Demo Listing Rating 3',
        listingType: ListingType.gomama,
        companyName: `Sentosa Development Corporation (SDC) Unverified`,
        addressName: 'Resorts World Station Level 1, Near Lift Unverified',
        description: 'Resorts World Station Level 1, Near Lift Unverified',
        fullAddress: '26 Sentosa Gateway, Singapore 098138',
        contactNumber: '',
        keywords: ['care', 'go!mama', 'breastfeeding'],
        postalCode: '098138',
        usageDurations: [30, 45],
        usageExtensionDurations: [15],
        maxNumberOfUsageExtensions: 1,
        numberOfPrivateFeedingRooms: 1,
        numberOfDiaperChangingMats: 1,
        diaperChangingMatType: 'Padded',
        countryDialCode: '65',
        openingHours: '7am-12am',
        piId: '',
        piLastUpdated: DateTime.fromFormat('2021-11-26 15:52:41', 'yyyy-MM-dd HH:mm:ss'),
        apiKey: '',
        lockId: '',
        lockMasterPin: '',
        doorIsLockable: true,
        isUsageExtendable: true,
        isVerified: false,
        isHidden: false,
        firestoreId: 'listingDemoRating3',
      },
    ])

    const listings = await Listing.query().whereIn('name', [
      'Demo Listing Rating 1',
      'Demo Listing Rating 2',
      'Demo Listing Rating 3',
    ])

    // Sessions For Listing 1
    const demoSessionsListing1 = await Session.createMany([
      // Demo already ended session
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo1',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo2',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo3',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo4',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo5NotRated',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
        actualEndedAt: DateTime.now(),
      },
      {
        listingId: listings[0].id,
        userId: demoUser.id,
        lockBluetoothGuestKey: 'demo6NotEnded',
        lockCustomPin: 'custompin',
        lockDailyPin: 'demo',
        lockHourlyPin: 'demo',
        lockOneTimePin: 'demo',
        startedAt: DateTime.now(),
        expectedEndedAt: DateTime.now(),
      },
    ])

    // Listing Rating for Listing 1's Session
    await ListingRating.createMany([
      {
        listingId: listings[0].id,
        sessionId: demoSessionsListing1[0].id,
        appRating: Math.random() * (5 - 0 + 0),
        experienceRating: Math.random() * (5 - 0 + 0),
        listingRating: Math.random() * (5 - 0 + 0),
        review: 'Demo listing rating for a session 1.',
        isHidden: false,
      },
      {
        listingId: listings[0].id,
        sessionId: demoSessionsListing1[1].id,
        appRating: Math.random() * (5 - 0 + 0),
        experienceRating: Math.random() * (5 - 0 + 0),
        listingRating: Math.random() * (5 - 0 + 0),
        review: 'Demo listing rating for a session 2.',
        isHidden: false,
      },
      {
        listingId: listings[0].id,
        sessionId: demoSessionsListing1[2].id,
        appRating: Math.random() * (5 - 0 + 0),
        experienceRating: Math.random() * (5 - 0 + 0),
        listingRating: Math.random() * (5 - 0 + 0),
        review: 'Demo listing rating for a session 3.',
        isHidden: false,
      },
      {
        listingId: listings[0].id,
        sessionId: demoSessionsListing1[3].id,
        appRating: Math.random() * (5 - 0 + 0),
        experienceRating: Math.random() * (5 - 0 + 0),
        listingRating: Math.random() * (5 - 0 + 0),
        review: 'Demo listing rating DELETE for a session 4.',
        isHidden: false,
      },
    ])
  }
}
