import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Amenity from 'App/Models/Amenity'
import { dirName, importData } from 'App/utils/dataImport'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (Env.get('SEEDER_RUN_TEST_CASE')) return
    const newRowArr: any[] = await importData(dirName.amenity)

    await Amenity.createMany(newRowArr)
  }
}
