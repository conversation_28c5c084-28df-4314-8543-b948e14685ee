import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import { dirName, importData } from 'App/utils/dataImport'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (Env.get('SEEDER_RUN_TEST_CASE')) return

    const newRowArr: any[] = await importData(dirName.users)
    // split the array into chunks of 100 (User now created immediately in data import, hence this is not needed, because it cause duplication)
    // const chunks = newRowArr.reduce((resultArray, item, index) => {
    //   const chunkIndex = Math.floor(index / 100)

    //   if (!resultArray[chunkIndex]) {
    //     resultArray[chunkIndex] = [] // start a new chunk
    //   }

    //   resultArray[chunkIndex].push(item)

    //   return resultArray
    // }, [])

    // for (let i = 0; i < chunks.length; i++) {
    //   console.log('chunk', i)
    //   await User.createMany(chunks[i])
    // }
  }
}
