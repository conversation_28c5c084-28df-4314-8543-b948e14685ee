import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Region from 'App/Models/Region'
import { dirName, importData } from 'App/utils/dataImport'
import Env from '@ioc:Adonis/Core/Env'

export default class extends BaseSeeder {
  public async run() {
    if (!Env.get('SEEDER_RUN_TEST_CASE')) return

    const newRowArr: any[] = await importData(dirName.regions)
    const regions = [
      'to delete region',
      'to delete region slug',
      'to update region',
      'to update region slug',
      'middle',
    ]
    const demoRegions = regions.map((region) => {
      return { name: region, imageUrl: `${region}.jpg`, description: region + ''.toLowerCase() }
    })

    await Region.createMany(newRowArr.concat(demoRegions))
  }
}
