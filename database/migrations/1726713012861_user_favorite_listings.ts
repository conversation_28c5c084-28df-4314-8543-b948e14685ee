import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'user_favorite_listings'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.unique(['listing_id', 'user_id'])
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['listing_id', 'user_id'])
    })
  }
}
