import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'one_time_passwords'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('email').alter().nullable()
    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('email').alter().notNullable()
    })
  }
}
