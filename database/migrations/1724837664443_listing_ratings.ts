import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'listing_ratings'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('session_id').alter().nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('session_id').alter().notNullable()
    })
  }
}
