/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/

import Route from '@ioc:Adonis/Core/Route'

// Protected Routes
Route.group(() => {
  Route.group(() => {
    Route.get('/', 'UsersController.findMyself')
    Route.delete('/', 'UsersController.deleteUser')
    Route.post('/logout', 'AuthController.logout')

    Route.get('/favorite-listings', 'UsersController.findMyFavoriteListings')
    Route.put('/favorite-listings', 'UsersController.updateFavoriteListing')

    Route.post('/gomama-verify', 'UsersController.gomamaVerify')
    Route.post('/gomama-verify/add-selfie-fail-count', 'UsersController.gomamaVerifySelfieCountAdd')
    Route.post('/singpass-verify', 'UsersController.singpassVerify')

    Route.get('/products/favourites', 'ShopifyController.getFavorites')
    Route.post('/products/favourites', 'ShopifyController.toggleFavourite')
    Route.put('/products/favourites', 'ShopifyController.setFavourites')

    Route.get('/orders', 'ShopifyController.getOrders')
    Route.get('/orders/reviews', 'ShopifyController.getOrdersToReview')
    Route.post('/orders/reviews', 'ShopifyController.submitReview')
    Route.put('/orders/:id/mark-as-received', 'ShopifyController.updateOrderReceived')

    Route.get('/coins/balance', 'CoinController.getBalance')
    Route.post('/coins/check-in', 'CoinController.dailyCheckIn')
    Route.post('/coins/max-redeemable', 'CoinController.getMaxRedeemable')
    Route.post('/coins/redeem', 'CoinController.redeemCoins')
    Route.post('/coins/cancel', 'CoinController.cancelRedemption')

    Route.post('/devices', 'DevicesController.create')
  }).prefix('/me')

  // Positions
  Route.get('/positions', 'PositionsController.findPositions')
  Route.get('/positions/:id', 'PositionsController.findPosition')

  // Users
  Route.get('/users', 'UsersController.findUsers')
  Route.get('/users/:id', 'UsersController.findUser')
  Route.put('/users', 'UsersController.updateUser')

  // Singpass
  Route.get('/singpassV4/myinfo/authorize', 'SingpassV4AuthsController.initializeSingpassOAuth')
  Route.get('/singpassV4/myinfo/callback', 'SingpassV4AuthsController.singpassMyInfoCallback')
  Route.get('/singpassV3/myinfo/authorize', 'SingpassV3AuthsController.initializeSingpassOAuth')
  Route.post('/singpassV3/myinfo/process', 'SingpassV3AuthsController.singpassMyInfoProcess')

  // Listing Type
  // Route.get('/listing-types', 'ListingTypesController.findListingTypes')

  // Listing Flag
  Route.post('/listing-flags/:listing_id', 'ListingFlagsController.createListingFlag')
  Route.get('/listing-flags', 'ListingFlagsController.retrieveListingFlags')
  Route.get('/listing-flags/:id', 'ListingFlagsController.retrieveListingFlag')

  // Listing Rating
  Route.get(
    '/listings/:id/listing-rating-summary',
    'ListingRatingsController.findListingRatingSummary'
  )
  Route.get('/listings/:id/listing-ratings', 'ListingRatingsController.findListingRatings')
  Route.get('/listing-ratings/:id', 'ListingRatingsController.findListingRating')
  Route.post('/listing-ratings', 'ListingRatingsController.createListingRating')
  Route.put('/listing-ratings/:id', 'ListingRatingsController.updateListingRating')

  // One Time Password
  Route.post(
    '/otp/verify-update-email-or-phone',
    'OneTimePasswordsController.matchOtpUpdateEmailOrPhone'
  )
  Route.post('/otp/update-email-or-phone', 'OneTimePasswordsController.createOtpUpdateEmailOrPhone')

  // Activities
  Route.get('/activities', 'ActivitiesController.findActivities')
  Route.get('/activities/slug/:slug', 'ActivitiesController.findActivityWithSlug')
  Route.get('/activities/:id', 'ActivitiesController.findActivity')

  // Amenities
  Route.get('/amenities', 'AmenitiesController.findAmenities')
  Route.get('/amenities/slug/:slug', 'AmenitiesController.findAmenityWithSlug')
  Route.get('/amenities/:id', 'AmenitiesController.findAmenity')

  // Regions
  Route.get('/regions', 'RegionsController.findRegions')
  Route.get('/regions/slug/:slug', 'RegionsController.findRegionWithSlug')
  Route.get('/regions/:id', 'RegionsController.findRegion')

  // Listings
  Route.get('/listings', 'ListingsController.findListings')
  Route.post('/listings', 'ListingsController.suggestListing')
  // Route.get('/listings/search-type/:listing_type_id', 'ListingsController.findListingByType')
  Route.get('/listings/search-position', 'ListingsController.findListingsByPosition')
  Route.get('/listings/search-keywords', 'ListingsController.findListingsByKeywords')
  Route.get('/listings/search-own-suggested', 'ListingsController.findSuggestedListings')
  Route.get('/listings/geojson', 'ListingsController.findListingsGeoJson')
  Route.get('/listings/:id', 'ListingsController.findListing')
  Route.post('/listings/:id/images', 'ListingsController.uploadListingImage')

  // Session
  Route.get('/sessions', 'SessionsController.findSessions')
  Route.post('/sessions', 'SessionsController.createSession')
  Route.get('/sessions/active', 'SessionsController.findActiveSession')
  Route.get('/sessions/:id', 'SessionsController.findSession')
  Route.post('/sessions/:id/regenerate-pins', 'SessionsController.regeneratePin')
  Route.post('/sessions/extend', 'SessionsController.extendSession')
  Route.post('/sessions/end', 'SessionsController.endSession')
})
  .prefix('/api/v1')
  .middleware('auth:api')

// Admin Routes
Route.post('/api/v1/admin/login', 'AuthController.adminLogin')

Route.group(() => {
  // me
  Route.get('/me', 'UsersController.findMyself')
  Route.post('/me/logout', 'AuthController.logoutAdmin')

  // Listing
  Route.post('/listings-quick', 'ListingsController.quickSuggestListingAdmin')
  Route.post('/listings', 'ListingsController.suggestListingAdmin')
  Route.get('/listings', 'ListingsController.findListingsAdmin')
  Route.get('/listings/search-position', 'ListingsController.findListingsByPositionAdmin')
  Route.get('/listings/search-keywords', 'ListingsController.findListingsByKeywordsAdmin')
  Route.get('/listings/:id', 'ListingsController.findListingAdmin')
  Route.put('/listings/:id', 'ListingsController.updateListingAdmin')
  Route.put('/listings/update-image/:id', 'ListingsController.updateListingImageAdmin')
  Route.delete('/listings/:id', 'ListingsController.deleteListing')

  // Listing Flag
  Route.post('/listing-flags/:listing_id', 'ListingFlagsController.createListingFlag')
  Route.get('/listing-flags', 'ListingFlagsController.retrieveAllListingFlagsAdmin')
  Route.get('/listing-flags/:id', 'ListingFlagsController.retrieveListingFlagAdmin')
  Route.put('/listing-flags/:id', 'ListingFlagsController.updateListingFlagAdmin')
  Route.delete('/listing-flags/:id', 'ListingFlagsController.deleteListingFlag')

  // Position
  Route.post('/positions', 'PositionsController.createPosition')
  Route.get('/positions', 'PositionsController.findPositionsAdmin')
  Route.get('/positions/:id', 'PositionsController.findPositionAdmin')
  Route.put('/positions/:id', 'PositionsController.updatePosition')
  Route.delete('/positions/:id', 'PositionsController.deletePosition')

  // Region
  Route.post('/regions', 'RegionsController.createRegion')
  Route.get('/regions', 'RegionsController.findRegionsAdmin')
  Route.get('/regions/:id', 'RegionsController.findRegionAdmin')
  Route.get('/regions/search-slug/:slug', 'RegionsController.findRegionWithSlugAdmin')
  Route.put('/regions/:id', 'RegionsController.updateRegion')
  Route.put('/regions/slug/:slug', 'RegionsController.updateRegionWithSlug')
  Route.delete('/regions/:id', 'RegionsController.deleteRegion')
  Route.delete('/regions/slug/:slug', 'RegionsController.deleteRegionWithSlug')

  // Activity
  Route.post('/activities', 'ActivitiesController.createActivity')
  Route.get('/activities', 'ActivitiesController.findActivitiesAdmin')
  Route.get('/activities/:id', 'ActivitiesController.findActivityAdmin')
  Route.get('/activities/search-slug/:slug', 'ActivitiesController.findActivityWithSlugAdmin')
  Route.put('/activities/:id', 'ActivitiesController.updateActivity')
  Route.put('/activities/slug/:slug', 'ActivitiesController.updateActivityWithSlug')
  Route.delete('/activities/:id', 'ActivitiesController.deleteActivity')
  Route.delete('/activities/slug/:slug', 'ActivitiesController.deleteActivityWithSlug')

  // Amenity
  Route.post('/amenities', 'AmenitiesController.createAmenity')
  Route.get('/amenities', 'AmenitiesController.findAmenitiesAdmin')
  Route.get('/amenities/:id', 'AmenitiesController.findAmenityAdmin')
  Route.get('/amenities/search-slug/:slug', 'AmenitiesController.findAmenityWithSlugAdmin')
  Route.put('/amenities/:id', 'AmenitiesController.updateAmenity')
  Route.put('/amenities/slug/:slug', 'AmenitiesController.updateAmenityWithSlug')
  Route.delete('/amenities/:id', 'AmenitiesController.deleteAmenity')
  Route.delete('/amenities/slug/:slug', 'AmenitiesController.deleteAmenityWithSlug')

  // User Type
  // Route.post('/user-types', 'UserTypesController.createUserType')
  // Route.get('/user-types', 'UserTypesController.findUserTypes')
  // Route.get('/user-types/:id', 'UserTypesController.findUserType')
  // Route.put('/user-types/:id', 'UserTypesController.updateUserType')
  // Route.delete('/user-types/:id', 'UserTypesController.deleteUserType')

  // User
  Route.post('/users', 'UsersController.createUserAdmin')
  Route.post('/users/review-verification-request/:id', 'UsersController.reviewVerification')
  Route.post('/users/reset-selfie-fail-count/:id', 'UsersController.resetUserSelfieVerifyFailCount')
  Route.get('/users', 'UsersController.findUsersAdmin')
  Route.get('/users/:id', 'UsersController.findUserAdmin')
  Route.get('/users/search-username/:username', 'UsersController.findUserByUsernameAdmin')
  Route.put('/users/:id', 'UsersController.updateUserAdmin')
  Route.put('/users/restore/:id', 'UsersController.restoreSoftDeleteAccount')
  Route.delete('/users/:id', 'UsersController.deleteUserAdmin')

  // Listing Type
  // Route.post('/listing-types', 'ListingTypesController.createListingType')
  // Route.get('/listing-types', 'ListingTypesController.findListingTypes')
  // Route.get('/listing-types/:id', 'ListingTypesController.findListingType')
  // Route.put('/listing-types/:id', 'ListingTypesController.updateListingType')
  // Route.delete('/listing-types/:id', 'ListingTypesController.deleteListingType')

  // Listing Rating
  Route.post('/listing-ratings', 'ListingRatingsController.createListingRating')
  Route.get('/listing-ratings', 'ListingRatingsController.findListingRatingsAdmin')
  Route.get('/listing-ratings/:id', 'ListingRatingsController.findListingRatingAdmin')
  Route.put('/listing-ratings/:id', 'ListingRatingsController.updateListingRating')
  Route.delete('/listing-ratings/:id', 'ListingRatingsController.deleteListingRating')

  // Session
  Route.post('/sessions', 'SessionsController.createSessionAdmin')
  Route.get('/sessions', 'SessionsController.findSessionsAdmin')
  Route.get('/sessions/:id', 'SessionsController.findSessionAdmin')
  Route.get('/sessions/search-user/:user_id', 'SessionsController.findSessionsByUserIdAdmin')
  Route.put('/sessions/:id/pins', 'SessionsController.updateSessionLockPins')
  Route.put('/sessions/:id/end', 'SessionsController.endSessionAdmin')
  Route.put('/sessions/:id/hide-unhide-session', 'SessionsController.hideNUnhideSessionAdmin')
  Route.delete('/sessions/:id', 'SessionsController.deleteSession')

  // Listing File
  Route.get('/listing-files', 'ListingFilesController.findListingFilesAdmin')
  Route.get('/listing-files/:id', 'ListingFilesController.findListingFileAdmin')

  // General Upload
  Route.post('/upload', 'FilesController.upload')

  // App Version Control
  Route.post('/app-version-controls', 'AppVersionControlsController.createVersion')
  Route.get(
    '/app-version-controls/latest',
    'AppVersionControlsController.retrieveLatestPublishedVersion'
  )
  Route.get('/app-version-controls', 'AppVersionControlsController.retrieveVersions')
  Route.get('/app-version-controls/:id', 'AppVersionControlsController.retrieveVersion')
  Route.put('/app-version-controls/:id', 'AppVersionControlsController.updateVersion')
  Route.put('/app-version-controls/publish/:id', 'AppVersionControlsController.publishVersion')
  Route.put('/app-version-controls/unpublish/:id', 'AppVersionControlsController.unpublishVersion')
  Route.delete('/app-version-controls/:id', 'AppVersionControlsController.deleteVersion')

  // Device
  Route.post('/devices', 'DevicesController.create')
  Route.get('/devices', 'DevicesController.index')
  Route.get('/devices/:id', 'DevicesController.show')
  Route.delete('/devices/:id', 'DevicesController.delete')

  // Notification Message
  Route.post('/notification-messages', 'NotificationMessagesController.create')
  Route.get('/notification-messages', 'NotificationMessagesController.index')
  Route.get('/notification-messages/:id', 'NotificationMessagesController.show')
  Route.put('/notification-messages/:id', 'NotificationMessagesController.update')
  Route.delete('/notification-messages/:id', 'NotificationMessagesController.delete')

  // Publish Message
  Route.post('/publish-messages', 'PublishedMessagesController.create')
  Route.get('/publish-messages', 'PublishedMessagesController.index')
  Route.get('/publish-messages/:id', 'PublishedMessagesController.show')
})
  .prefix('/api/v1/admin')
  .middleware(['auth:web', 'admin'])

// Public Routes
Route.group(() => {
  Route.get('/listing-files/:id', 'ListingFilesController.findListingFile')

  // SINGPASS webhook
  // Route.get('/verification/callback', async ({ view }) => {
  //   return view.render('singpass_callback')
  // })
  Route.get('/verification/callback', ({ response }) => {
    return response.redirect().withQs().toPath('https://app.gomama.com.sg/singpass/myinfo/callback')
  })

  Route.post('/login', 'AuthController.login')
  Route.post('/login-otp', 'AuthController.loginWithOTP')
  Route.get('/users/search-username/:username', 'UsersController.findUserByUsername')
  Route.post('/users', 'UsersController.createUser')

  Route.post('/otp/verify-registration-otp', 'OneTimePasswordsController.matchOtpRegistration')
  Route.post('/otp/register-or-login', 'OneTimePasswordsController.createOtpLoginOrRegister')

  // App Version Control
  Route.get(
    '/app-version-controls/latest',
    'AppVersionControlsController.retrieveLatestPublishedVersion'
  )

  // OAuth
  Route.post('/oauth/google', 'OAuthController.loginGoogle')
  Route.post('/oauth/facebook', 'OAuthController.loginFacebook')
  Route.post('/oauth/apple', 'OAuthController.loginApple')
}).prefix('/api/v1')

// Internal routes between realtime & core
Route.group(() => {
  Route.get('/auth', 'AuthController.realtimeVerify')
})
  .prefix('/api/v1/realtime')
  .middleware('auth:api')

// Internal routes between other microservices & core
Route.group(() => {
  Route.get('/users/:id', 'UsersController.findUserAdmin')
})
  .prefix('/api/v1/service')
  .middleware('apiKey')

// DEBUG
// Import Firebase LevelDB Data
Route.post('/test/import-data', 'DataImportsController.importJsonFile')

// Healthcheck
Route.get('/health', ({ response }) => {
  return response.ok({ status: 'ok' })
})

// Privacy Policy
Route.get('/privacy-policy', async ({ view }) => {
  return view.render('privacy_policy')
})
