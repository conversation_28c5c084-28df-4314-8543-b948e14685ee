/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import Env from '@ioc:Adonis/Core/Env'
import Event from '@ioc:Adonis/Core/Event'
import Logger from '@ioc:Adonis/Core/Logger'
// import { PublishCommand, SNSClient } from '@aws-sdk/client-sns'
import { fromEnv } from '@aws-sdk/credential-providers'
import { syncMeilisearch, updateGeojsonFile } from 'App/utils'
import { SendEmailCommand, SendEmailCommandInput, SESClient } from '@aws-sdk/client-ses'
// import { Twilio } from 'twilio'
import axios from 'axios'

// const twilioClient = new Twilio(
//   Env.get('TWILIO_ACCOUNT_SID'),
//   Env.get('TWILIO_AUTH_TOKEN')
// )

Event.on('new:otp-email', async (emailObject) => {
  const { html, ...rest } = emailObject
  Logger.info(rest, 'new:otp-email')

  const sesClient = new SESClient({ credentials: fromEnv(), region: Env.get('SNS_REGION') })

  const params: SendEmailCommandInput = {
    Destination: {
      ToAddresses: [emailObject.to],
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: emailObject.html,
        },
        Text: {
          Charset: 'UTF-8',
          Data: emailObject.text,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: emailObject.subject,
      },
    },
    Source: emailObject.from,
  }

  try {
    const data = await sesClient.send(new SendEmailCommand(params))

    Logger.info(data, 'new:otp-email')
  } catch (error) {
    Logger.error(error, 'new:otp-email')
  }
})

Event.on('new:otp-phone', async (smsPhoneObject) => {
  Logger.info(smsPhoneObject, 'new:otp-phone')

  // const snsClient = new SNSClient({ credentials: fromEnv(), region: Env.get('SNS_REGION') })

  // const params = {
  //   Message: smsPhoneObject.phoneMessage,
  //   PhoneNumber: smsPhoneObject.to,
  //   MessageAttributes: {
  //     'AWS.SNS.SMS.SenderID': {
  //       DataType: 'String',
  //       StringValue: 'YourApp',
  //     },
  //     'AWS.SNS.SMS.SMSType': {
  //       DataType: 'String',
  //       StringValue: 'Transactional',
  //     },
  //   },
  // }

  // try {
  //   const command = new PublishCommand(params)
  //   const smsResponse = await snsClient.send(command)

  //   Logger.info(smsResponse, 'new:otp-phone')
  // } catch (err) {
  //   Logger.info(err.message, 'new:otp-phone')
  // }

  try {
    // const message = await twilioClient.messages.create({
    //   body: smsPhoneObject.phoneMessage,
    //   from: Env.get('TWILIO_PHONE_NUMBER'),
    //   to: smsPhoneObject.to
    // })

    await axios.post(
      `http://gateway.onewaysms.sg:10002/api.aspx?apiusername=${Env.get(
        'ONEWAYSMS_USERNAME'
      )}&apipassword=${Env.get('ONEWAYSMS_PASSWORD')}&mobileno=${
        smsPhoneObject.to
      }&senderid=${Env.get('ONEWAYSMS_SENDER_ID')}&languagetype=${1}&message=${
        smsPhoneObject.phoneMessage
      }`
    )

    Logger.info({ message: smsPhoneObject.phoneMessage }, 'new:otp-phone:success')

    // Logger.info({ messageId: message.sid }, 'new:otp-phone:success')
  } catch (error) {
    Logger.error(error, 'new:otp-phone')
    throw error
  }
})

Event.on('update:geojson', async () => {
  Logger.info('update:geojson')
  updateGeojsonFile()
})

Event.on('sync:meilisearch', async (syncMeiliMeta) => {
  Logger.info(syncMeiliMeta, 'sync:meilisearch')
  syncMeilisearch(syncMeiliMeta.type, syncMeiliMeta.listingId)
})
